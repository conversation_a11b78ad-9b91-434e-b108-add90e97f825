.class public final Lcom/github/catvod/spider/merge/G/g;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/util/Iterator;
.implements Lcom/github/catvod/spider/merge/D/a;


# instance fields
.field public final a:Ljava/util/Iterator;

.field public final synthetic b:Lcom/github/catvod/spider/merge/G/h;


# direct methods
.method public constructor <init>(Lcom/github/catvod/spider/merge/G/h;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lcom/github/catvod/spider/merge/G/g;->b:Lcom/github/catvod/spider/merge/G/h;

    .line 5
    .line 6
    iget-object p1, p1, Lcom/github/catvod/spider/merge/G/h;->c:Ljava/lang/Object;

    .line 7
    .line 8
    check-cast p1, Lcom/github/catvod/spider/merge/G/b;

    .line 9
    .line 10
    invoke-interface {p1}, Lcom/github/catvod/spider/merge/G/b;->iterator()Ljava/util/Iterator;

    .line 11
    .line 12
    .line 13
    move-result-object p1

    .line 14
    iput-object p1, p0, Lcom/github/catvod/spider/merge/G/g;->a:Ljava/util/Iterator;

    .line 15
    .line 16
    return-void
.end method


# virtual methods
.method public final hasNext()Z
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/github/catvod/spider/merge/G/g;->a:Ljava/util/Iterator;

    .line 2
    .line 3
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    return v0
.end method

.method public final next()Ljava/lang/Object;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/github/catvod/spider/merge/G/g;->b:Lcom/github/catvod/spider/merge/G/h;

    .line 2
    .line 3
    iget-object v0, v0, Lcom/github/catvod/spider/merge/G/h;->b:Lcom/github/catvod/spider/merge/C/g;

    .line 4
    .line 5
    iget-object v1, p0, Lcom/github/catvod/spider/merge/G/g;->a:Ljava/util/Iterator;

    .line 6
    .line 7
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    invoke-interface {v0, v1}, Lcom/github/catvod/spider/merge/B/l;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    return-object v0
.end method

.method public final remove()V
    .locals 2

    .line 1
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    .line 2
    .line 3
    const-string v1, "Operation is not supported for read-only collection"

    .line 4
    .line 5
    invoke-direct {v0, v1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    throw v0
.end method
