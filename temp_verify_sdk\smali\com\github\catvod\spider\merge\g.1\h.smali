.class public final Lcom/github/catvod/spider/merge/g/h;
.super Ljava/lang/Object;
.source "SourceFile"


# instance fields
.field public final a:Ljava/util/Map;

.field public final b:Z

.field public final c:Ljava/util/List;


# direct methods
.method public constructor <init>(Ljava/util/Map;ZLjava/util/List;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lcom/github/catvod/spider/merge/g/h;->a:Ljava/util/Map;

    .line 5
    .line 6
    iput-boolean p2, p0, Lcom/github/catvod/spider/merge/g/h;->b:Z

    .line 7
    .line 8
    iput-object p3, p0, Lcom/github/catvod/spider/merge/g/h;->c:Ljava/util/List;

    .line 9
    .line 10
    return-void
.end method

.method public static a(Ljava/lang/Class;)Ljava/lang/String;
    .locals 2

    .line 1
    invoke-virtual {p0}, Ljava/lang/Class;->getModifiers()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    invoke-static {v0}, Ljava/lang/reflect/Modifier;->isInterface(I)Z

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    if-eqz v1, :cond_0

    .line 10
    .line 11
    invoke-virtual {p0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    .line 12
    .line 13
    .line 14
    move-result-object p0

    .line 15
    const-string v0, "Interfaces can\'t be instantiated! Register an InstanceCreator or a TypeAdapter for this type. Interface name: "

    .line 16
    .line 17
    invoke-virtual {v0, p0}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    .line 18
    .line 19
    .line 20
    move-result-object p0

    .line 21
    return-object p0

    .line 22
    :cond_0
    invoke-static {v0}, Ljava/lang/reflect/Modifier;->isAbstract(I)Z

    .line 23
    .line 24
    .line 25
    move-result v0

    .line 26
    if-eqz v0, :cond_1

    .line 27
    .line 28
    new-instance v0, Ljava/lang/StringBuilder;

    .line 29
    .line 30
    const-string v1, "Abstract classes can\'t be instantiated! Adjust the R8 configuration or register an InstanceCreator or a TypeAdapter for this type. Class name: "

    .line 31
    .line 32
    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 33
    .line 34
    .line 35
    invoke-virtual {p0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    .line 36
    .line 37
    .line 38
    move-result-object p0

    .line 39
    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 40
    .line 41
    .line 42
    const-string p0, "\nSee "

    .line 43
    .line 44
    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 45
    .line 46
    .line 47
    const-string p0, "https://github.com/google/gson/blob/main/Troubleshooting.md#"

    .line 48
    .line 49
    const-string v1, "r8-abstract-class"

    .line 50
    .line 51
    invoke-virtual {p0, v1}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    .line 52
    .line 53
    .line 54
    move-result-object p0

    .line 55
    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 56
    .line 57
    .line 58
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 59
    .line 60
    .line 61
    move-result-object p0

    .line 62
    return-object p0

    .line 63
    :cond_1
    const/4 p0, 0x0

    .line 64
    return-object p0
.end method


# virtual methods
.method public final b(Lcom/github/catvod/spider/merge/k/a;)Lcom/github/catvod/spider/merge/g/r;
    .locals 11

    .line 1
    const/4 v0, 0x3

    .line 2
    const/4 v1, 0x4

    .line 3
    const/4 v2, 0x2

    .line 4
    const/4 v3, 0x1

    .line 5
    const/4 v4, 0x0

    .line 6
    iget-object v5, p0, Lcom/github/catvod/spider/merge/g/h;->a:Ljava/util/Map;

    .line 7
    .line 8
    iget-object v6, p1, Lcom/github/catvod/spider/merge/k/a;->b:Ljava/lang/reflect/Type;

    .line 9
    .line 10
    invoke-interface {v5, v6}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 11
    .line 12
    .line 13
    move-result-object v7

    .line 14
    if-nez v7, :cond_14

    .line 15
    .line 16
    iget-object p1, p1, Lcom/github/catvod/spider/merge/k/a;->a:Ljava/lang/Class;

    .line 17
    .line 18
    invoke-interface {v5, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 19
    .line 20
    .line 21
    move-result-object v5

    .line 22
    if-nez v5, :cond_13

    .line 23
    .line 24
    const-class v5, Ljava/util/EnumSet;

    .line 25
    .line 26
    invoke-virtual {v5, p1}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    .line 27
    .line 28
    .line 29
    move-result v5

    .line 30
    const/4 v7, 0x0

    .line 31
    if-eqz v5, :cond_0

    .line 32
    .line 33
    new-instance v5, Lcom/github/catvod/spider/merge/g/g;

    .line 34
    .line 35
    invoke-direct {v5, v6, v4}, Lcom/github/catvod/spider/merge/g/g;-><init>(Ljava/lang/reflect/Type;I)V

    .line 36
    .line 37
    .line 38
    goto :goto_0

    .line 39
    :cond_0
    const-class v5, Ljava/util/EnumMap;

    .line 40
    .line 41
    if-ne p1, v5, :cond_1

    .line 42
    .line 43
    new-instance v5, Lcom/github/catvod/spider/merge/g/g;

    .line 44
    .line 45
    invoke-direct {v5, v6, v3}, Lcom/github/catvod/spider/merge/g/g;-><init>(Ljava/lang/reflect/Type;I)V

    .line 46
    .line 47
    .line 48
    goto :goto_0

    .line 49
    :cond_1
    move-object v5, v7

    .line 50
    :goto_0
    if-eqz v5, :cond_2

    .line 51
    .line 52
    return-object v5

    .line 53
    :cond_2
    iget-object v5, p0, Lcom/github/catvod/spider/merge/g/h;->c:Ljava/util/List;

    .line 54
    .line 55
    invoke-static {v5}, Lcom/github/catvod/spider/merge/g/d;->e(Ljava/util/List;)V

    .line 56
    .line 57
    .line 58
    invoke-virtual {p1}, Ljava/lang/Class;->getModifiers()I

    .line 59
    .line 60
    .line 61
    move-result v5

    .line 62
    invoke-static {v5}, Ljava/lang/reflect/Modifier;->isAbstract(I)Z

    .line 63
    .line 64
    .line 65
    move-result v5

    .line 66
    if-eqz v5, :cond_3

    .line 67
    .line 68
    :goto_1
    move-object v5, v7

    .line 69
    goto :goto_3

    .line 70
    :cond_3
    :try_start_0
    new-array v5, v4, [Ljava/lang/Class;

    .line 71
    .line 72
    invoke-virtual {p1, v5}, Ljava/lang/Class;->getDeclaredConstructor([Ljava/lang/Class;)Ljava/lang/reflect/Constructor;

    .line 73
    .line 74
    .line 75
    move-result-object v5
    :try_end_0
    .catch Ljava/lang/NoSuchMethodException; {:try_start_0 .. :try_end_0} :catch_1

    .line 76
    sget-object v8, Lcom/github/catvod/spider/merge/i/c;->a:Lcom/github/catvod/spider/merge/A/a;

    .line 77
    .line 78
    :try_start_1
    invoke-virtual {v5, v3}, Ljava/lang/reflect/AccessibleObject;->setAccessible(Z)V
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0

    .line 79
    .line 80
    .line 81
    move-object v8, v7

    .line 82
    goto :goto_2

    .line 83
    :catch_0
    move-exception v8

    .line 84
    new-instance v9, Ljava/lang/StringBuilder;

    .line 85
    .line 86
    const-string v10, "Failed making constructor \'"

    .line 87
    .line 88
    invoke-direct {v9, v10}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 89
    .line 90
    .line 91
    invoke-static {v5}, Lcom/github/catvod/spider/merge/i/c;->b(Ljava/lang/reflect/Constructor;)Ljava/lang/String;

    .line 92
    .line 93
    .line 94
    move-result-object v10

    .line 95
    invoke-virtual {v9, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 96
    .line 97
    .line 98
    const-string v10, "\' accessible; either increase its visibility or write a custom InstanceCreator or TypeAdapter for its declaring type: "

    .line 99
    .line 100
    invoke-virtual {v9, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 101
    .line 102
    .line 103
    invoke-virtual {v8}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    .line 104
    .line 105
    .line 106
    move-result-object v10

    .line 107
    invoke-virtual {v9, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 108
    .line 109
    .line 110
    invoke-static {v8}, Lcom/github/catvod/spider/merge/i/c;->e(Ljava/lang/Exception;)Ljava/lang/String;

    .line 111
    .line 112
    .line 113
    move-result-object v8

    .line 114
    invoke-virtual {v9, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 115
    .line 116
    .line 117
    invoke-virtual {v9}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 118
    .line 119
    .line 120
    move-result-object v8

    .line 121
    :goto_2
    if-eqz v8, :cond_4

    .line 122
    .line 123
    new-instance v5, Lcom/github/catvod/spider/merge/g/f;

    .line 124
    .line 125
    invoke-direct {v5, v8, v2}, Lcom/github/catvod/spider/merge/g/f;-><init>(Ljava/lang/String;I)V

    .line 126
    .line 127
    .line 128
    goto :goto_3

    .line 129
    :cond_4
    new-instance v8, Lcom/github/catvod/spider/merge/G/e;

    .line 130
    .line 131
    invoke-direct {v8, v1, v5}, Lcom/github/catvod/spider/merge/G/e;-><init>(ILjava/lang/Object;)V

    .line 132
    .line 133
    .line 134
    move-object v5, v8

    .line 135
    goto :goto_3

    .line 136
    :catch_1
    nop

    .line 137
    goto :goto_1

    .line 138
    :goto_3
    if-eqz v5, :cond_5

    .line 139
    .line 140
    return-object v5

    .line 141
    :cond_5
    const-class v5, Ljava/util/Collection;

    .line 142
    .line 143
    invoke-virtual {v5, p1}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    .line 144
    .line 145
    .line 146
    move-result v5

    .line 147
    if-eqz v5, :cond_9

    .line 148
    .line 149
    const-class v1, Ljava/util/SortedSet;

    .line 150
    .line 151
    invoke-virtual {v1, p1}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    .line 152
    .line 153
    .line 154
    move-result v1

    .line 155
    if-eqz v1, :cond_6

    .line 156
    .line 157
    new-instance v7, Lcom/github/catvod/spider/merge/g/e;

    .line 158
    .line 159
    invoke-direct {v7, v4}, Lcom/github/catvod/spider/merge/g/e;-><init>(I)V

    .line 160
    .line 161
    .line 162
    goto/16 :goto_4

    .line 163
    .line 164
    :cond_6
    const-class v1, Ljava/util/Set;

    .line 165
    .line 166
    invoke-virtual {v1, p1}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    .line 167
    .line 168
    .line 169
    move-result v1

    .line 170
    if-eqz v1, :cond_7

    .line 171
    .line 172
    new-instance v7, Lcom/github/catvod/spider/merge/g/e;

    .line 173
    .line 174
    invoke-direct {v7, v3}, Lcom/github/catvod/spider/merge/g/e;-><init>(I)V

    .line 175
    .line 176
    .line 177
    goto/16 :goto_4

    .line 178
    .line 179
    :cond_7
    const-class v1, Ljava/util/Queue;

    .line 180
    .line 181
    invoke-virtual {v1, p1}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    .line 182
    .line 183
    .line 184
    move-result v1

    .line 185
    if-eqz v1, :cond_8

    .line 186
    .line 187
    new-instance v7, Lcom/github/catvod/spider/merge/g/e;

    .line 188
    .line 189
    invoke-direct {v7, v2}, Lcom/github/catvod/spider/merge/g/e;-><init>(I)V

    .line 190
    .line 191
    .line 192
    goto :goto_4

    .line 193
    :cond_8
    new-instance v7, Lcom/github/catvod/spider/merge/g/e;

    .line 194
    .line 195
    invoke-direct {v7, v0}, Lcom/github/catvod/spider/merge/g/e;-><init>(I)V

    .line 196
    .line 197
    .line 198
    goto :goto_4

    .line 199
    :cond_9
    const-class v2, Ljava/util/Map;

    .line 200
    .line 201
    invoke-virtual {v2, p1}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    .line 202
    .line 203
    .line 204
    move-result v2

    .line 205
    if-eqz v2, :cond_e

    .line 206
    .line 207
    const-class v2, Ljava/util/concurrent/ConcurrentNavigableMap;

    .line 208
    .line 209
    invoke-virtual {v2, p1}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    .line 210
    .line 211
    .line 212
    move-result v2

    .line 213
    if-eqz v2, :cond_a

    .line 214
    .line 215
    new-instance v7, Lcom/github/catvod/spider/merge/g/e;

    .line 216
    .line 217
    invoke-direct {v7, v1}, Lcom/github/catvod/spider/merge/g/e;-><init>(I)V

    .line 218
    .line 219
    .line 220
    goto :goto_4

    .line 221
    :cond_a
    const-class v1, Ljava/util/concurrent/ConcurrentMap;

    .line 222
    .line 223
    invoke-virtual {v1, p1}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    .line 224
    .line 225
    .line 226
    move-result v1

    .line 227
    if-eqz v1, :cond_b

    .line 228
    .line 229
    new-instance v7, Lcom/github/catvod/spider/merge/g/e;

    .line 230
    .line 231
    const/4 v1, 0x5

    .line 232
    invoke-direct {v7, v1}, Lcom/github/catvod/spider/merge/g/e;-><init>(I)V

    .line 233
    .line 234
    .line 235
    goto :goto_4

    .line 236
    :cond_b
    const-class v1, Ljava/util/SortedMap;

    .line 237
    .line 238
    invoke-virtual {v1, p1}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    .line 239
    .line 240
    .line 241
    move-result v1

    .line 242
    if-eqz v1, :cond_c

    .line 243
    .line 244
    new-instance v7, Lcom/github/catvod/spider/merge/g/e;

    .line 245
    .line 246
    const/4 v1, 0x6

    .line 247
    invoke-direct {v7, v1}, Lcom/github/catvod/spider/merge/g/e;-><init>(I)V

    .line 248
    .line 249
    .line 250
    goto :goto_4

    .line 251
    :cond_c
    instance-of v1, v6, Ljava/lang/reflect/ParameterizedType;

    .line 252
    .line 253
    if-eqz v1, :cond_d

    .line 254
    .line 255
    check-cast v6, Ljava/lang/reflect/ParameterizedType;

    .line 256
    .line 257
    invoke-interface {v6}, Ljava/lang/reflect/ParameterizedType;->getActualTypeArguments()[Ljava/lang/reflect/Type;

    .line 258
    .line 259
    .line 260
    move-result-object v1

    .line 261
    aget-object v1, v1, v4

    .line 262
    .line 263
    new-instance v2, Lcom/github/catvod/spider/merge/k/a;

    .line 264
    .line 265
    invoke-direct {v2, v1}, Lcom/github/catvod/spider/merge/k/a;-><init>(Ljava/lang/reflect/Type;)V

    .line 266
    .line 267
    .line 268
    const-class v1, Ljava/lang/String;

    .line 269
    .line 270
    iget-object v2, v2, Lcom/github/catvod/spider/merge/k/a;->a:Ljava/lang/Class;

    .line 271
    .line 272
    invoke-virtual {v1, v2}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    .line 273
    .line 274
    .line 275
    move-result v1

    .line 276
    if-nez v1, :cond_d

    .line 277
    .line 278
    new-instance v7, Lcom/github/catvod/spider/merge/g/e;

    .line 279
    .line 280
    const/4 v1, 0x7

    .line 281
    invoke-direct {v7, v1}, Lcom/github/catvod/spider/merge/g/e;-><init>(I)V

    .line 282
    .line 283
    .line 284
    goto :goto_4

    .line 285
    :cond_d
    new-instance v7, Lcom/github/catvod/spider/merge/g/e;

    .line 286
    .line 287
    const/16 v1, 0x8

    .line 288
    .line 289
    invoke-direct {v7, v1}, Lcom/github/catvod/spider/merge/g/e;-><init>(I)V

    .line 290
    .line 291
    .line 292
    :cond_e
    :goto_4
    if-eqz v7, :cond_f

    .line 293
    .line 294
    return-object v7

    .line 295
    :cond_f
    invoke-static {p1}, Lcom/github/catvod/spider/merge/g/h;->a(Ljava/lang/Class;)Ljava/lang/String;

    .line 296
    .line 297
    .line 298
    move-result-object v1

    .line 299
    if-eqz v1, :cond_10

    .line 300
    .line 301
    new-instance p1, Lcom/github/catvod/spider/merge/g/f;

    .line 302
    .line 303
    invoke-direct {p1, v1, v3}, Lcom/github/catvod/spider/merge/g/f;-><init>(Ljava/lang/String;I)V

    .line 304
    .line 305
    .line 306
    return-object p1

    .line 307
    :cond_10
    iget-boolean v1, p0, Lcom/github/catvod/spider/merge/g/h;->b:Z

    .line 308
    .line 309
    if-eqz v1, :cond_11

    .line 310
    .line 311
    new-instance v1, Lcom/github/catvod/spider/merge/G/e;

    .line 312
    .line 313
    invoke-direct {v1, v0, p1}, Lcom/github/catvod/spider/merge/G/e;-><init>(ILjava/lang/Object;)V

    .line 314
    .line 315
    .line 316
    goto :goto_5

    .line 317
    :cond_11
    new-instance v0, Ljava/lang/StringBuilder;

    .line 318
    .line 319
    const-string v1, "Unable to create instance of "

    .line 320
    .line 321
    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 322
    .line 323
    .line 324
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 325
    .line 326
    .line 327
    const-string v1, "; usage of JDK Unsafe is disabled. Registering an InstanceCreator or a TypeAdapter for this type, adding a no-args constructor, or enabling usage of JDK Unsafe may fix this problem."

    .line 328
    .line 329
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 330
    .line 331
    .line 332
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 333
    .line 334
    .line 335
    move-result-object v0

    .line 336
    invoke-virtual {p1}, Ljava/lang/Class;->getDeclaredConstructors()[Ljava/lang/reflect/Constructor;

    .line 337
    .line 338
    .line 339
    move-result-object p1

    .line 340
    array-length p1, p1

    .line 341
    if-nez p1, :cond_12

    .line 342
    .line 343
    new-instance p1, Ljava/lang/StringBuilder;

    .line 344
    .line 345
    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    .line 346
    .line 347
    .line 348
    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 349
    .line 350
    .line 351
    const-string v0, " Or adjust your R8 configuration to keep the no-args constructor of the class."

    .line 352
    .line 353
    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 354
    .line 355
    .line 356
    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 357
    .line 358
    .line 359
    move-result-object v0

    .line 360
    :cond_12
    new-instance v1, Lcom/github/catvod/spider/merge/g/f;

    .line 361
    .line 362
    invoke-direct {v1, v0, v4}, Lcom/github/catvod/spider/merge/g/f;-><init>(Ljava/lang/String;I)V

    .line 363
    .line 364
    .line 365
    :goto_5
    return-object v1

    .line 366
    :cond_13
    new-instance p1, Ljava/lang/ClassCastException;

    .line 367
    .line 368
    invoke-direct {p1}, Ljava/lang/ClassCastException;-><init>()V

    .line 369
    .line 370
    .line 371
    throw p1

    .line 372
    :cond_14
    new-instance p1, Ljava/lang/ClassCastException;

    .line 373
    .line 374
    invoke-direct {p1}, Ljava/lang/ClassCastException;-><init>()V

    .line 375
    .line 376
    .line 377
    throw p1
.end method

.method public final toString()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/github/catvod/spider/merge/g/h;->a:Ljava/util/Map;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method
