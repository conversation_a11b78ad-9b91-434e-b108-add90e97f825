.class interface abstract Lorg/simpleframework/xml/stream/EventNode;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Iterable;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ljava/lang/Iterable<",
        "Lorg/simpleframework/xml/stream/Attribute;",
        ">;"
    }
.end annotation


# virtual methods
.method public abstract getLine()I
.end method

.method public abstract getName()Ljava/lang/String;
.end method

.method public abstract getPrefix()Ljava/lang/String;
.end method

.method public abstract getReference()Ljava/lang/String;
.end method

.method public abstract getSource()Ljava/lang/Object;
.end method

.method public abstract getValue()Ljava/lang/String;
.end method

.method public abstract isEnd()Z
.end method

.method public abstract isStart()Z
.end method

.method public abstract isText()Z
.end method
