.class public final Lcom/github/catvod/spider/merge/h/j;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/github/catvod/spider/merge/e/A;


# static fields
.field public static final c:Lcom/github/catvod/spider/merge/h/i;


# instance fields
.field public final a:Lcom/github/catvod/spider/merge/g/h;

.field public final b:Ljava/util/concurrent/ConcurrentHashMap;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, Lcom/github/catvod/spider/merge/h/i;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, Lcom/github/catvod/spider/merge/h/i;-><init>(I)V

    .line 5
    .line 6
    .line 7
    sput-object v0, Lcom/github/catvod/spider/merge/h/j;->c:Lcom/github/catvod/spider/merge/h/i;

    .line 8
    .line 9
    new-instance v0, Lcom/github/catvod/spider/merge/h/i;

    .line 10
    .line 11
    invoke-direct {v0, v1}, Lcom/github/catvod/spider/merge/h/i;-><init>(I)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public constructor <init>(Lcom/github/catvod/spider/merge/g/h;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lcom/github/catvod/spider/merge/h/j;->a:Lcom/github/catvod/spider/merge/g/h;

    .line 5
    .line 6
    new-instance p1, Ljava/util/concurrent/ConcurrentHashMap;

    .line 7
    .line 8
    invoke-direct {p1}, Ljava/util/concurrent/ConcurrentHashMap;-><init>()V

    .line 9
    .line 10
    .line 11
    iput-object p1, p0, Lcom/github/catvod/spider/merge/h/j;->b:Ljava/util/concurrent/ConcurrentHashMap;

    .line 12
    .line 13
    return-void
.end method


# virtual methods
.method public final a(Lcom/github/catvod/spider/merge/e/n;Lcom/github/catvod/spider/merge/k/a;)Lcom/github/catvod/spider/merge/e/z;
    .locals 7

    .line 1
    const-class v0, Lcom/github/catvod/spider/merge/f/a;

    .line 2
    .line 3
    iget-object v1, p2, Lcom/github/catvod/spider/merge/k/a;->a:Ljava/lang/Class;

    .line 4
    .line 5
    invoke-virtual {v1, v0}, Ljava/lang/Class;->getAnnotation(Ljava/lang/Class;)Ljava/lang/annotation/Annotation;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    move-object v5, v0

    .line 10
    check-cast v5, Lcom/github/catvod/spider/merge/f/a;

    .line 11
    .line 12
    if-nez v5, :cond_0

    .line 13
    .line 14
    const/4 p1, 0x0

    .line 15
    return-object p1

    .line 16
    :cond_0
    iget-object v2, p0, Lcom/github/catvod/spider/merge/h/j;->a:Lcom/github/catvod/spider/merge/g/h;

    .line 17
    .line 18
    const/4 v6, 0x1

    .line 19
    move-object v1, p0

    .line 20
    move-object v3, p1

    .line 21
    move-object v4, p2

    .line 22
    invoke-virtual/range {v1 .. v6}, Lcom/github/catvod/spider/merge/h/j;->b(Lcom/github/catvod/spider/merge/g/h;Lcom/github/catvod/spider/merge/e/n;Lcom/github/catvod/spider/merge/k/a;Lcom/github/catvod/spider/merge/f/a;Z)Lcom/github/catvod/spider/merge/e/z;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    return-object p1
.end method

.method public final b(Lcom/github/catvod/spider/merge/g/h;Lcom/github/catvod/spider/merge/e/n;Lcom/github/catvod/spider/merge/k/a;Lcom/github/catvod/spider/merge/f/a;Z)Lcom/github/catvod/spider/merge/e/z;
    .locals 2

    .line 1
    invoke-interface {p4}, Lcom/github/catvod/spider/merge/f/a;->value()Ljava/lang/Class;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    new-instance v1, Lcom/github/catvod/spider/merge/k/a;

    .line 6
    .line 7
    invoke-direct {v1, v0}, Lcom/github/catvod/spider/merge/k/a;-><init>(Ljava/lang/reflect/Type;)V

    .line 8
    .line 9
    .line 10
    invoke-virtual {p1, v1}, Lcom/github/catvod/spider/merge/g/h;->b(Lcom/github/catvod/spider/merge/k/a;)Lcom/github/catvod/spider/merge/g/r;

    .line 11
    .line 12
    .line 13
    move-result-object p1

    .line 14
    invoke-interface {p1}, Lcom/github/catvod/spider/merge/g/r;->a()Ljava/lang/Object;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    invoke-interface {p4}, Lcom/github/catvod/spider/merge/f/a;->nullSafe()Z

    .line 19
    .line 20
    .line 21
    move-result p4

    .line 22
    instance-of v0, p1, Lcom/github/catvod/spider/merge/e/z;

    .line 23
    .line 24
    if-eqz v0, :cond_0

    .line 25
    .line 26
    check-cast p1, Lcom/github/catvod/spider/merge/e/z;

    .line 27
    .line 28
    goto :goto_0

    .line 29
    :cond_0
    instance-of v0, p1, Lcom/github/catvod/spider/merge/e/A;

    .line 30
    .line 31
    if-eqz v0, :cond_3

    .line 32
    .line 33
    check-cast p1, Lcom/github/catvod/spider/merge/e/A;

    .line 34
    .line 35
    if-eqz p5, :cond_1

    .line 36
    .line 37
    iget-object p5, p0, Lcom/github/catvod/spider/merge/h/j;->b:Ljava/util/concurrent/ConcurrentHashMap;

    .line 38
    .line 39
    iget-object v0, p3, Lcom/github/catvod/spider/merge/k/a;->a:Ljava/lang/Class;

    .line 40
    .line 41
    invoke-virtual {p5, v0, p1}, Ljava/util/concurrent/ConcurrentHashMap;->putIfAbsent(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 42
    .line 43
    .line 44
    move-result-object p5

    .line 45
    check-cast p5, Lcom/github/catvod/spider/merge/e/A;

    .line 46
    .line 47
    if-eqz p5, :cond_1

    .line 48
    .line 49
    move-object p1, p5

    .line 50
    :cond_1
    invoke-interface {p1, p2, p3}, Lcom/github/catvod/spider/merge/e/A;->a(Lcom/github/catvod/spider/merge/e/n;Lcom/github/catvod/spider/merge/k/a;)Lcom/github/catvod/spider/merge/e/z;

    .line 51
    .line 52
    .line 53
    move-result-object p1

    .line 54
    :goto_0
    if-eqz p1, :cond_2

    .line 55
    .line 56
    if-eqz p4, :cond_2

    .line 57
    .line 58
    new-instance p2, Lcom/github/catvod/spider/merge/e/l;

    .line 59
    .line 60
    const/4 p3, 0x2

    .line 61
    invoke-direct {p2, p1, p3}, Lcom/github/catvod/spider/merge/e/l;-><init>(Lcom/github/catvod/spider/merge/e/z;I)V

    .line 62
    .line 63
    .line 64
    return-object p2

    .line 65
    :cond_2
    return-object p1

    .line 66
    :cond_3
    new-instance p2, Ljava/lang/IllegalArgumentException;

    .line 67
    .line 68
    new-instance p4, Ljava/lang/StringBuilder;

    .line 69
    .line 70
    const-string p5, "Invalid attempt to bind an instance of "

    .line 71
    .line 72
    invoke-direct {p4, p5}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 73
    .line 74
    .line 75
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 76
    .line 77
    .line 78
    move-result-object p1

    .line 79
    invoke-virtual {p1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    .line 80
    .line 81
    .line 82
    move-result-object p1

    .line 83
    invoke-virtual {p4, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 84
    .line 85
    .line 86
    const-string p1, " as a @JsonAdapter for "

    .line 87
    .line 88
    invoke-virtual {p4, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 89
    .line 90
    .line 91
    iget-object p1, p3, Lcom/github/catvod/spider/merge/k/a;->b:Ljava/lang/reflect/Type;

    .line 92
    .line 93
    invoke-static {p1}, Lcom/github/catvod/spider/merge/g/d;->i(Ljava/lang/reflect/Type;)Ljava/lang/String;

    .line 94
    .line 95
    .line 96
    move-result-object p1

    .line 97
    invoke-virtual {p4, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 98
    .line 99
    .line 100
    const-string p1, ". @JsonAdapter value must be a TypeAdapter, TypeAdapterFactory, JsonSerializer or JsonDeserializer."

    .line 101
    .line 102
    invoke-virtual {p4, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 103
    .line 104
    .line 105
    invoke-virtual {p4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 106
    .line 107
    .line 108
    move-result-object p1

    .line 109
    invoke-direct {p2, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 110
    .line 111
    .line 112
    throw p2
.end method
