.class interface abstract Lorg/simpleframework/xml/core/Instance;
.super Ljava/lang/Object;
.source "SourceFile"


# virtual methods
.method public abstract getInstance()Ljava/lang/Object;
.end method

.method public abstract getType()Ljava/lang/Class;
.end method

.method public abstract isReference()Z
.end method

.method public abstract setInstance(Ljava/lang/Object;)Ljava/lang/Object;
.end method
