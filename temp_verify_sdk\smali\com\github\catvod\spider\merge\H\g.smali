.class public final Lcom/github/catvod/spider/merge/H/g;
.super Lcom/github/catvod/spider/merge/q/a;
.source "SourceFile"


# instance fields
.field public final synthetic a:Lcom/github/catvod/spider/merge/H/h;


# direct methods
.method public constructor <init>(Lcom/github/catvod/spider/merge/H/h;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lcom/github/catvod/spider/merge/H/g;->a:Lcom/github/catvod/spider/merge/H/h;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final a(I)Lcom/github/catvod/spider/merge/H/c;
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/github/catvod/spider/merge/H/g;->a:Lcom/github/catvod/spider/merge/H/h;

    .line 2
    .line 3
    iget-object v1, v0, Lcom/github/catvod/spider/merge/H/h;->a:<PERSON><PERSON><PERSON>/lang/Object;

    .line 4
    .line 5
    check-cast v1, <PERSON><PERSON><PERSON>/util/regex/Matcher;

    .line 6
    .line 7
    invoke-virtual {v1, p1}, Ljava/util/regex/Matcher;->start(I)I

    .line 8
    .line 9
    .line 10
    move-result v2

    .line 11
    invoke-virtual {v1, p1}, Ljava/util/regex/Matcher;->end(I)I

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    invoke-static {v2, v1}, Lcom/github/catvod/spider/merge/A/a;->t(II)Lcom/github/catvod/spider/merge/E/f;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    iget v2, v1, Lcom/github/catvod/spider/merge/E/d;->a:I

    .line 20
    .line 21
    if-ltz v2, :cond_0

    .line 22
    .line 23
    new-instance v2, Lcom/github/catvod/spider/merge/H/c;

    .line 24
    .line 25
    iget-object v0, v0, Lcom/github/catvod/spider/merge/H/h;->a:Ljava/lang/Object;

    .line 26
    .line 27
    check-cast v0, Ljava/util/regex/Matcher;

    .line 28
    .line 29
    invoke-virtual {v0, p1}, Ljava/util/regex/Matcher;->group(I)Ljava/lang/String;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    const-string v0, "group(...)"

    .line 34
    .line 35
    invoke-static {p1, v0}, Lcom/github/catvod/spider/merge/C/f;->d(Ljava/lang/Object;Ljava/lang/String;)V

    .line 36
    .line 37
    .line 38
    invoke-direct {v2, p1, v1}, Lcom/github/catvod/spider/merge/H/c;-><init>(Ljava/lang/String;Lcom/github/catvod/spider/merge/E/f;)V

    .line 39
    .line 40
    .line 41
    return-object v2

    .line 42
    :cond_0
    const/4 p1, 0x0

    .line 43
    return-object p1
.end method

.method public final bridge contains(Ljava/lang/Object;)Z
    .locals 1

    .line 1
    if-nez p1, :cond_0

    .line 2
    .line 3
    const/4 v0, 0x1

    .line 4
    goto :goto_0

    .line 5
    :cond_0
    instance-of v0, p1, Lcom/github/catvod/spider/merge/H/c;

    .line 6
    .line 7
    :goto_0
    if-nez v0, :cond_1

    .line 8
    .line 9
    const/4 p1, 0x0

    .line 10
    return p1

    .line 11
    :cond_1
    check-cast p1, Lcom/github/catvod/spider/merge/H/c;

    .line 12
    .line 13
    invoke-super {p0, p1}, Lcom/github/catvod/spider/merge/q/a;->contains(Ljava/lang/Object;)Z

    .line 14
    .line 15
    .line 16
    move-result p1

    .line 17
    return p1
.end method

.method public final getSize()I
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/github/catvod/spider/merge/H/g;->a:Lcom/github/catvod/spider/merge/H/h;

    .line 2
    .line 3
    iget-object v0, v0, Lcom/github/catvod/spider/merge/H/h;->a:Ljava/lang/Object;

    .line 4
    .line 5
    check-cast v0, Ljava/util/regex/Matcher;

    .line 6
    .line 7
    invoke-virtual {v0}, Ljava/util/regex/Matcher;->groupCount()I

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    add-int/lit8 v0, v0, 0x1

    .line 12
    .line 13
    return v0
.end method

.method public final isEmpty()Z
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    return v0
.end method

.method public final iterator()Ljava/util/Iterator;
    .locals 4

    .line 1
    new-instance v0, Lcom/github/catvod/spider/merge/E/f;

    .line 2
    .line 3
    invoke-interface {p0}, Ljava/util/Collection;->size()I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    const/4 v2, 0x1

    .line 8
    sub-int/2addr v1, v2

    .line 9
    const/4 v3, 0x0

    .line 10
    invoke-direct {v0, v3, v1, v2}, Lcom/github/catvod/spider/merge/E/d;-><init>(III)V

    .line 11
    .line 12
    .line 13
    new-instance v1, Lcom/github/catvod/spider/merge/G/e;

    .line 14
    .line 15
    const/4 v2, 0x5

    .line 16
    invoke-direct {v1, v2, v0}, Lcom/github/catvod/spider/merge/G/e;-><init>(ILjava/lang/Object;)V

    .line 17
    .line 18
    .line 19
    new-instance v0, Lcom/github/catvod/spider/merge/H/f;

    .line 20
    .line 21
    const/4 v2, 0x0

    .line 22
    invoke-direct {v0, v2, p0}, Lcom/github/catvod/spider/merge/H/f;-><init>(ILjava/lang/Object;)V

    .line 23
    .line 24
    .line 25
    new-instance v2, Lcom/github/catvod/spider/merge/G/h;

    .line 26
    .line 27
    invoke-direct {v2, v1, v0}, Lcom/github/catvod/spider/merge/G/h;-><init>(Lcom/github/catvod/spider/merge/G/b;Lcom/github/catvod/spider/merge/B/l;)V

    .line 28
    .line 29
    .line 30
    new-instance v0, Lcom/github/catvod/spider/merge/G/g;

    .line 31
    .line 32
    invoke-direct {v0, v2}, Lcom/github/catvod/spider/merge/G/g;-><init>(Lcom/github/catvod/spider/merge/G/h;)V

    .line 33
    .line 34
    .line 35
    return-object v0
.end method
