.class public final Lcom/github/catvod/spider/merge/e/n;
.super Ljava/lang/Object;
.source "SourceFile"


# static fields
.field public static final t:Lcom/github/catvod/spider/merge/e/i;

.field public static final u:Lcom/github/catvod/spider/merge/e/a;

.field public static final v:Lcom/github/catvod/spider/merge/e/u;

.field public static final w:Lcom/github/catvod/spider/merge/e/v;


# instance fields
.field public final a:Ljava/lang/ThreadLocal;

.field public final b:Ljava/util/concurrent/ConcurrentHashMap;

.field public final c:Lcom/github/catvod/spider/merge/g/h;

.field public final d:Lcom/github/catvod/spider/merge/h/j;

.field public final e:Ljava/util/List;

.field public final f:Lcom/github/catvod/spider/merge/g/j;

.field public final g:Lcom/github/catvod/spider/merge/e/h;

.field public final h:Ljava/util/Map;

.field public final i:Z

.field public final j:Lcom/github/catvod/spider/merge/e/i;

.field public final k:Z

.field public final l:I

.field public final m:I

.field public final n:Ljava/util/List;

.field public final o:Ljava/util/List;

.field public final p:Lcom/github/catvod/spider/merge/e/y;

.field public final q:Lcom/github/catvod/spider/merge/e/y;

.field public final r:Ljava/util/List;

.field public final s:I


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    sget-object v0, Lcom/github/catvod/spider/merge/e/i;->d:Lcom/github/catvod/spider/merge/e/i;

    .line 2
    .line 3
    sput-object v0, Lcom/github/catvod/spider/merge/e/n;->t:Lcom/github/catvod/spider/merge/e/i;

    .line 4
    .line 5
    sget-object v0, Lcom/github/catvod/spider/merge/e/h;->a:Lcom/github/catvod/spider/merge/e/a;

    .line 6
    .line 7
    sput-object v0, Lcom/github/catvod/spider/merge/e/n;->u:Lcom/github/catvod/spider/merge/e/a;

    .line 8
    .line 9
    sget-object v0, Lcom/github/catvod/spider/merge/e/y;->a:Lcom/github/catvod/spider/merge/e/u;

    .line 10
    .line 11
    sput-object v0, Lcom/github/catvod/spider/merge/e/n;->v:Lcom/github/catvod/spider/merge/e/u;

    .line 12
    .line 13
    sget-object v0, Lcom/github/catvod/spider/merge/e/y;->b:Lcom/github/catvod/spider/merge/e/v;

    .line 14
    .line 15
    sput-object v0, Lcom/github/catvod/spider/merge/e/n;->w:Lcom/github/catvod/spider/merge/e/v;

    .line 16
    .line 17
    return-void
.end method

.method public constructor <init>(Lcom/github/catvod/spider/merge/g/j;Lcom/github/catvod/spider/merge/e/h;Ljava/util/Map;ZLcom/github/catvod/spider/merge/e/i;ZIIILjava/util/List;Ljava/util/List;Ljava/util/List;Lcom/github/catvod/spider/merge/e/y;Lcom/github/catvod/spider/merge/e/y;Ljava/util/List;)V
    .locals 7

    move-object/from16 v2, p13

    move-object/from16 v3, p14

    move-object/from16 v4, p15

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    new-instance v5, Ljava/lang/ThreadLocal;

    invoke-direct {v5}, Ljava/lang/ThreadLocal;-><init>()V

    iput-object v5, p0, Lcom/github/catvod/spider/merge/e/n;->a:Ljava/lang/ThreadLocal;

    .line 3
    new-instance v5, Ljava/util/concurrent/ConcurrentHashMap;

    invoke-direct {v5}, Ljava/util/concurrent/ConcurrentHashMap;-><init>()V

    iput-object v5, p0, Lcom/github/catvod/spider/merge/e/n;->b:Ljava/util/concurrent/ConcurrentHashMap;

    .line 4
    iput-object p1, p0, Lcom/github/catvod/spider/merge/e/n;->f:Lcom/github/catvod/spider/merge/g/j;

    .line 5
    iput-object p2, p0, Lcom/github/catvod/spider/merge/e/n;->g:Lcom/github/catvod/spider/merge/e/h;

    .line 6
    iput-object p3, p0, Lcom/github/catvod/spider/merge/e/n;->h:Ljava/util/Map;

    .line 7
    new-instance v5, Lcom/github/catvod/spider/merge/g/h;

    invoke-direct {v5, p3, p6, v4}, Lcom/github/catvod/spider/merge/g/h;-><init>(Ljava/util/Map;ZLjava/util/List;)V

    iput-object v5, p0, Lcom/github/catvod/spider/merge/e/n;->c:Lcom/github/catvod/spider/merge/g/h;

    .line 8
    iput-boolean p4, p0, Lcom/github/catvod/spider/merge/e/n;->i:Z

    .line 9
    iput-object p5, p0, Lcom/github/catvod/spider/merge/e/n;->j:Lcom/github/catvod/spider/merge/e/i;

    .line 10
    iput-boolean p6, p0, Lcom/github/catvod/spider/merge/e/n;->k:Z

    .line 11
    iput p7, p0, Lcom/github/catvod/spider/merge/e/n;->s:I

    .line 12
    iput p8, p0, Lcom/github/catvod/spider/merge/e/n;->l:I

    move/from16 p3, p9

    .line 13
    iput p3, p0, Lcom/github/catvod/spider/merge/e/n;->m:I

    move-object/from16 p3, p10

    .line 14
    iput-object p3, p0, Lcom/github/catvod/spider/merge/e/n;->n:Ljava/util/List;

    move-object/from16 p3, p11

    .line 15
    iput-object p3, p0, Lcom/github/catvod/spider/merge/e/n;->o:Ljava/util/List;

    .line 16
    iput-object v2, p0, Lcom/github/catvod/spider/merge/e/n;->p:Lcom/github/catvod/spider/merge/e/y;

    .line 17
    iput-object v3, p0, Lcom/github/catvod/spider/merge/e/n;->q:Lcom/github/catvod/spider/merge/e/y;

    .line 18
    iput-object v4, p0, Lcom/github/catvod/spider/merge/e/n;->r:Ljava/util/List;

    .line 19
    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 20
    sget-object p3, Lcom/github/catvod/spider/merge/h/e0;->z:Lcom/github/catvod/spider/merge/h/U;

    invoke-virtual {v0, p3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 21
    sget-object p3, Lcom/github/catvod/spider/merge/e/y;->a:Lcom/github/catvod/spider/merge/e/u;

    if-ne v2, p3, :cond_0

    .line 22
    sget-object p3, Lcom/github/catvod/spider/merge/h/o;->b:Lcom/github/catvod/spider/merge/h/n;

    goto :goto_0

    .line 23
    :cond_0
    new-instance p3, Lcom/github/catvod/spider/merge/h/n;

    invoke-direct {p3, v2}, Lcom/github/catvod/spider/merge/h/n;-><init>(Lcom/github/catvod/spider/merge/e/y;)V

    .line 24
    :goto_0
    invoke-virtual {v0, p3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 25
    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    move-object/from16 p3, p12

    .line 26
    invoke-virtual {v0, p3}, Ljava/util/ArrayList;->addAll(Ljava/util/Collection;)Z

    .line 27
    sget-object p3, Lcom/github/catvod/spider/merge/h/e0;->p:Lcom/github/catvod/spider/merge/h/U;

    invoke-virtual {v0, p3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 28
    sget-object p3, Lcom/github/catvod/spider/merge/h/e0;->g:Lcom/github/catvod/spider/merge/h/V;

    invoke-virtual {v0, p3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 29
    sget-object p3, Lcom/github/catvod/spider/merge/h/e0;->d:Lcom/github/catvod/spider/merge/h/V;

    invoke-virtual {v0, p3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 30
    sget-object p3, Lcom/github/catvod/spider/merge/h/e0;->e:Lcom/github/catvod/spider/merge/h/V;

    invoke-virtual {v0, p3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 31
    sget-object p3, Lcom/github/catvod/spider/merge/h/e0;->f:Lcom/github/catvod/spider/merge/h/V;

    invoke-virtual {v0, p3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    const/4 p3, 0x1

    if-ne p7, p3, :cond_1

    .line 32
    sget-object p3, Lcom/github/catvod/spider/merge/h/e0;->k:Lcom/github/catvod/spider/merge/h/y;

    goto :goto_1

    .line 33
    :cond_1
    new-instance p3, Lcom/github/catvod/spider/merge/e/k;

    invoke-direct {p3}, Lcom/github/catvod/spider/merge/e/k;-><init>()V

    .line 34
    :goto_1
    new-instance p4, Lcom/github/catvod/spider/merge/h/V;

    sget-object v1, Ljava/lang/Long;->TYPE:Ljava/lang/Class;

    const-class v2, Ljava/lang/Long;

    invoke-direct {p4, v1, v2, p3}, Lcom/github/catvod/spider/merge/h/V;-><init>(Ljava/lang/Class;Ljava/lang/Class;Lcom/github/catvod/spider/merge/e/z;)V

    .line 35
    invoke-virtual {v0, p4}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 36
    new-instance p4, Lcom/github/catvod/spider/merge/e/j;

    const/4 v1, 0x0

    .line 37
    invoke-direct {p4, v1}, Lcom/github/catvod/spider/merge/e/j;-><init>(I)V

    .line 38
    new-instance v1, Lcom/github/catvod/spider/merge/h/V;

    sget-object v2, Ljava/lang/Double;->TYPE:Ljava/lang/Class;

    const-class v6, Ljava/lang/Double;

    invoke-direct {v1, v2, v6, p4}, Lcom/github/catvod/spider/merge/h/V;-><init>(Ljava/lang/Class;Ljava/lang/Class;Lcom/github/catvod/spider/merge/e/z;)V

    .line 39
    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 40
    new-instance p4, Lcom/github/catvod/spider/merge/e/j;

    const/4 v1, 0x1

    .line 41
    invoke-direct {p4, v1}, Lcom/github/catvod/spider/merge/e/j;-><init>(I)V

    .line 42
    new-instance v1, Lcom/github/catvod/spider/merge/h/V;

    sget-object v2, Ljava/lang/Float;->TYPE:Ljava/lang/Class;

    const-class v6, Ljava/lang/Float;

    invoke-direct {v1, v2, v6, p4}, Lcom/github/catvod/spider/merge/h/V;-><init>(Ljava/lang/Class;Ljava/lang/Class;Lcom/github/catvod/spider/merge/e/z;)V

    .line 43
    invoke-virtual {v0, v1}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 44
    sget-object p4, Lcom/github/catvod/spider/merge/e/y;->b:Lcom/github/catvod/spider/merge/e/v;

    if-ne v3, p4, :cond_2

    .line 45
    sget-object p4, Lcom/github/catvod/spider/merge/h/m;->b:Lcom/github/catvod/spider/merge/h/l;

    goto :goto_2

    .line 46
    :cond_2
    new-instance p4, Lcom/github/catvod/spider/merge/h/m;

    const/4 v1, 0x0

    .line 47
    invoke-direct {p4, v1}, Lcom/github/catvod/spider/merge/h/m;-><init>(I)V

    .line 48
    new-instance v1, Lcom/github/catvod/spider/merge/h/l;

    const/4 v2, 0x0

    invoke-direct {v1, p4, v2}, Lcom/github/catvod/spider/merge/h/l;-><init>(Lcom/github/catvod/spider/merge/e/z;I)V

    move-object p4, v1

    .line 49
    :goto_2
    invoke-virtual {v0, p4}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 50
    sget-object p4, Lcom/github/catvod/spider/merge/h/e0;->h:Lcom/github/catvod/spider/merge/h/U;

    invoke-virtual {v0, p4}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 51
    sget-object p4, Lcom/github/catvod/spider/merge/h/e0;->i:Lcom/github/catvod/spider/merge/h/U;

    invoke-virtual {v0, p4}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 52
    new-instance p4, Lcom/github/catvod/spider/merge/e/l;

    const/4 v1, 0x0

    invoke-direct {p4, p3, v1}, Lcom/github/catvod/spider/merge/e/l;-><init>(Lcom/github/catvod/spider/merge/e/z;I)V

    .line 53
    new-instance v1, Lcom/github/catvod/spider/merge/e/l;

    const/4 v2, 0x2

    invoke-direct {v1, p4, v2}, Lcom/github/catvod/spider/merge/e/l;-><init>(Lcom/github/catvod/spider/merge/e/z;I)V

    .line 54
    new-instance p4, Lcom/github/catvod/spider/merge/h/U;

    const-class v2, Ljava/util/concurrent/atomic/AtomicLong;

    const/4 v3, 0x0

    invoke-direct {p4, v2, v1, v3}, Lcom/github/catvod/spider/merge/h/U;-><init>(Ljava/lang/Class;Lcom/github/catvod/spider/merge/e/z;I)V

    .line 55
    invoke-virtual {v0, p4}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 56
    new-instance p4, Lcom/github/catvod/spider/merge/e/l;

    const/4 v1, 0x1

    invoke-direct {p4, p3, v1}, Lcom/github/catvod/spider/merge/e/l;-><init>(Lcom/github/catvod/spider/merge/e/z;I)V

    .line 57
    new-instance p3, Lcom/github/catvod/spider/merge/e/l;

    const/4 v1, 0x2

    invoke-direct {p3, p4, v1}, Lcom/github/catvod/spider/merge/e/l;-><init>(Lcom/github/catvod/spider/merge/e/z;I)V

    .line 58
    new-instance p4, Lcom/github/catvod/spider/merge/h/U;

    const-class v1, Ljava/util/concurrent/atomic/AtomicLongArray;

    const/4 v2, 0x0

    invoke-direct {p4, v1, p3, v2}, Lcom/github/catvod/spider/merge/h/U;-><init>(Ljava/lang/Class;Lcom/github/catvod/spider/merge/e/z;I)V

    .line 59
    invoke-virtual {v0, p4}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 60
    sget-object p3, Lcom/github/catvod/spider/merge/h/e0;->j:Lcom/github/catvod/spider/merge/h/U;

    invoke-virtual {v0, p3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 61
    sget-object p3, Lcom/github/catvod/spider/merge/h/e0;->l:Lcom/github/catvod/spider/merge/h/V;

    invoke-virtual {v0, p3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 62
    sget-object p3, Lcom/github/catvod/spider/merge/h/e0;->q:Lcom/github/catvod/spider/merge/h/U;

    invoke-virtual {v0, p3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 63
    sget-object p3, Lcom/github/catvod/spider/merge/h/e0;->r:Lcom/github/catvod/spider/merge/h/U;

    invoke-virtual {v0, p3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 64
    sget-object p3, Lcom/github/catvod/spider/merge/h/e0;->m:Lcom/github/catvod/spider/merge/h/D;

    .line 65
    new-instance p4, Lcom/github/catvod/spider/merge/h/U;

    const-class v1, Ljava/math/BigDecimal;

    const/4 v2, 0x0

    invoke-direct {p4, v1, p3, v2}, Lcom/github/catvod/spider/merge/h/U;-><init>(Ljava/lang/Class;Lcom/github/catvod/spider/merge/e/z;I)V

    .line 66
    invoke-virtual {v0, p4}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 67
    sget-object p3, Lcom/github/catvod/spider/merge/h/e0;->n:Lcom/github/catvod/spider/merge/h/E;

    .line 68
    new-instance p4, Lcom/github/catvod/spider/merge/h/U;

    const-class v1, Ljava/math/BigInteger;

    const/4 v2, 0x0

    invoke-direct {p4, v1, p3, v2}, Lcom/github/catvod/spider/merge/h/U;-><init>(Ljava/lang/Class;Lcom/github/catvod/spider/merge/e/z;I)V

    .line 69
    invoke-virtual {v0, p4}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 70
    sget-object p3, Lcom/github/catvod/spider/merge/h/e0;->o:Lcom/github/catvod/spider/merge/h/F;

    .line 71
    new-instance p4, Lcom/github/catvod/spider/merge/h/U;

    const-class v1, Lcom/github/catvod/spider/merge/g/l;

    const/4 v2, 0x0

    invoke-direct {p4, v1, p3, v2}, Lcom/github/catvod/spider/merge/h/U;-><init>(Ljava/lang/Class;Lcom/github/catvod/spider/merge/e/z;I)V

    .line 72
    invoke-virtual {v0, p4}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 73
    sget-object p3, Lcom/github/catvod/spider/merge/h/e0;->s:Lcom/github/catvod/spider/merge/h/U;

    invoke-virtual {v0, p3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 74
    sget-object p3, Lcom/github/catvod/spider/merge/h/e0;->t:Lcom/github/catvod/spider/merge/h/U;

    invoke-virtual {v0, p3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 75
    sget-object p3, Lcom/github/catvod/spider/merge/h/e0;->v:Lcom/github/catvod/spider/merge/h/U;

    invoke-virtual {v0, p3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 76
    sget-object p3, Lcom/github/catvod/spider/merge/h/e0;->w:Lcom/github/catvod/spider/merge/h/U;

    invoke-virtual {v0, p3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 77
    sget-object p3, Lcom/github/catvod/spider/merge/h/e0;->y:Lcom/github/catvod/spider/merge/h/U;

    invoke-virtual {v0, p3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 78
    sget-object p3, Lcom/github/catvod/spider/merge/h/e0;->u:Lcom/github/catvod/spider/merge/h/U;

    invoke-virtual {v0, p3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 79
    sget-object p3, Lcom/github/catvod/spider/merge/h/e0;->b:Lcom/github/catvod/spider/merge/h/U;

    invoke-virtual {v0, p3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 80
    sget-object p3, Lcom/github/catvod/spider/merge/h/h;->b:Lcom/github/catvod/spider/merge/h/e;

    invoke-virtual {v0, p3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 81
    sget-object p3, Lcom/github/catvod/spider/merge/h/e0;->x:Lcom/github/catvod/spider/merge/h/l;

    invoke-virtual {v0, p3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 82
    sget-boolean p3, Lcom/github/catvod/spider/merge/j/h;->a:Z

    if-eqz p3, :cond_3

    .line 83
    sget-object p3, Lcom/github/catvod/spider/merge/j/h;->e:Lcom/github/catvod/spider/merge/j/c;

    invoke-virtual {v0, p3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 84
    sget-object p3, Lcom/github/catvod/spider/merge/j/h;->d:Lcom/github/catvod/spider/merge/j/a;

    invoke-virtual {v0, p3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 85
    sget-object p3, Lcom/github/catvod/spider/merge/j/h;->f:Lcom/github/catvod/spider/merge/j/e;

    invoke-virtual {v0, p3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 86
    :cond_3
    sget-object p3, Lcom/github/catvod/spider/merge/h/b;->b:Lcom/github/catvod/spider/merge/h/a;

    invoke-virtual {v0, p3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 87
    sget-object p3, Lcom/github/catvod/spider/merge/h/e0;->a:Lcom/github/catvod/spider/merge/h/U;

    invoke-virtual {v0, p3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 88
    new-instance p3, Lcom/github/catvod/spider/merge/h/d;

    const/4 p4, 0x0

    invoke-direct {p3, v5, p4}, Lcom/github/catvod/spider/merge/h/d;-><init>(Lcom/github/catvod/spider/merge/g/h;I)V

    invoke-virtual {v0, p3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 89
    new-instance p3, Lcom/github/catvod/spider/merge/h/d;

    const/4 p4, 0x1

    invoke-direct {p3, v5, p4}, Lcom/github/catvod/spider/merge/h/d;-><init>(Lcom/github/catvod/spider/merge/g/h;I)V

    invoke-virtual {v0, p3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 90
    new-instance p3, Lcom/github/catvod/spider/merge/h/j;

    invoke-direct {p3, v5}, Lcom/github/catvod/spider/merge/h/j;-><init>(Lcom/github/catvod/spider/merge/g/h;)V

    iput-object p3, p0, Lcom/github/catvod/spider/merge/e/n;->d:Lcom/github/catvod/spider/merge/h/j;

    .line 91
    invoke-virtual {v0, p3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 92
    sget-object p4, Lcom/github/catvod/spider/merge/h/e0;->A:Lcom/github/catvod/spider/merge/h/S;

    invoke-virtual {v0, p4}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 93
    new-instance p4, Lcom/github/catvod/spider/merge/h/u;

    move-object p6, p1

    move-object p5, p2

    move-object p7, p3

    move-object p3, p4

    move-object p8, v4

    move-object p4, v5

    invoke-direct/range {p3 .. p8}, Lcom/github/catvod/spider/merge/h/u;-><init>(Lcom/github/catvod/spider/merge/g/h;Lcom/github/catvod/spider/merge/e/h;Lcom/github/catvod/spider/merge/g/j;Lcom/github/catvod/spider/merge/h/j;Ljava/util/List;)V

    invoke-virtual {v0, p3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 94
    invoke-static {v0}, Ljava/util/Collections;->unmodifiableList(Ljava/util/List;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Lcom/github/catvod/spider/merge/e/n;->e:Ljava/util/List;

    return-void
.end method

.method public static a(D)V
    .locals 2

    .line 1
    invoke-static {p0, p1}, Ljava/lang/Double;->isNaN(D)Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-nez v0, :cond_0

    .line 6
    .line 7
    invoke-static {p0, p1}, Ljava/lang/Double;->isInfinite(D)Z

    .line 8
    .line 9
    .line 10
    move-result v0

    .line 11
    if-nez v0, :cond_0

    .line 12
    .line 13
    return-void

    .line 14
    :cond_0
    new-instance v0, Ljava/lang/IllegalArgumentException;

    .line 15
    .line 16
    new-instance v1, Ljava/lang/StringBuilder;

    .line 17
    .line 18
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 19
    .line 20
    .line 21
    invoke-virtual {v1, p0, p1}, Ljava/lang/StringBuilder;->append(D)Ljava/lang/StringBuilder;

    .line 22
    .line 23
    .line 24
    const-string p0, " is not a valid double value as per JSON specification. To override this behavior, use GsonBuilder.serializeSpecialFloatingPointValues() method."

    .line 25
    .line 26
    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 27
    .line 28
    .line 29
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 30
    .line 31
    .line 32
    move-result-object p0

    .line 33
    invoke-direct {v0, p0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 34
    .line 35
    .line 36
    throw v0
.end method


# virtual methods
.method public final b(Lcom/github/catvod/spider/merge/k/a;)Lcom/github/catvod/spider/merge/e/z;
    .locals 8

    .line 1
    iget-object v0, p0, Lcom/github/catvod/spider/merge/e/n;->b:Ljava/util/concurrent/ConcurrentHashMap;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Ljava/util/concurrent/ConcurrentHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v1

    .line 7
    check-cast v1, Lcom/github/catvod/spider/merge/e/z;

    .line 8
    .line 9
    if-eqz v1, :cond_0

    .line 10
    .line 11
    return-object v1

    .line 12
    :cond_0
    iget-object v1, p0, Lcom/github/catvod/spider/merge/e/n;->a:Ljava/lang/ThreadLocal;

    .line 13
    .line 14
    invoke-virtual {v1}, Ljava/lang/ThreadLocal;->get()Ljava/lang/Object;

    .line 15
    .line 16
    .line 17
    move-result-object v2

    .line 18
    check-cast v2, Ljava/util/Map;

    .line 19
    .line 20
    if-nez v2, :cond_1

    .line 21
    .line 22
    new-instance v2, Ljava/util/HashMap;

    .line 23
    .line 24
    invoke-direct {v2}, Ljava/util/HashMap;-><init>()V

    .line 25
    .line 26
    .line 27
    invoke-virtual {v1, v2}, Ljava/lang/ThreadLocal;->set(Ljava/lang/Object;)V

    .line 28
    .line 29
    .line 30
    const/4 v3, 0x1

    .line 31
    goto :goto_0

    .line 32
    :cond_1
    invoke-interface {v2, p1}, Ljava/util/Map;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 33
    .line 34
    .line 35
    move-result-object v3

    .line 36
    check-cast v3, Lcom/github/catvod/spider/merge/e/z;

    .line 37
    .line 38
    if-eqz v3, :cond_2

    .line 39
    .line 40
    return-object v3

    .line 41
    :cond_2
    const/4 v3, 0x0

    .line 42
    :goto_0
    :try_start_0
    new-instance v4, Lcom/github/catvod/spider/merge/e/m;

    .line 43
    .line 44
    invoke-direct {v4}, Lcom/github/catvod/spider/merge/e/m;-><init>()V

    .line 45
    .line 46
    .line 47
    invoke-interface {v2, p1, v4}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 48
    .line 49
    .line 50
    iget-object v5, p0, Lcom/github/catvod/spider/merge/e/n;->e:Ljava/util/List;

    .line 51
    .line 52
    invoke-interface {v5}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    .line 53
    .line 54
    .line 55
    move-result-object v5

    .line 56
    const/4 v6, 0x0

    .line 57
    :cond_3
    invoke-interface {v5}, Ljava/util/Iterator;->hasNext()Z

    .line 58
    .line 59
    .line 60
    move-result v7

    .line 61
    if-eqz v7, :cond_5

    .line 62
    .line 63
    invoke-interface {v5}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 64
    .line 65
    .line 66
    move-result-object v6

    .line 67
    check-cast v6, Lcom/github/catvod/spider/merge/e/A;

    .line 68
    .line 69
    invoke-interface {v6, p0, p1}, Lcom/github/catvod/spider/merge/e/A;->a(Lcom/github/catvod/spider/merge/e/n;Lcom/github/catvod/spider/merge/k/a;)Lcom/github/catvod/spider/merge/e/z;

    .line 70
    .line 71
    .line 72
    move-result-object v6

    .line 73
    if-eqz v6, :cond_3

    .line 74
    .line 75
    iget-object v5, v4, Lcom/github/catvod/spider/merge/e/m;->a:Lcom/github/catvod/spider/merge/e/z;

    .line 76
    .line 77
    if-nez v5, :cond_4

    .line 78
    .line 79
    iput-object v6, v4, Lcom/github/catvod/spider/merge/e/m;->a:Lcom/github/catvod/spider/merge/e/z;

    .line 80
    .line 81
    invoke-interface {v2, p1, v6}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 82
    .line 83
    .line 84
    goto :goto_1

    .line 85
    :catchall_0
    move-exception p1

    .line 86
    goto :goto_2

    .line 87
    :cond_4
    new-instance p1, Ljava/lang/AssertionError;

    .line 88
    .line 89
    const-string v0, "Delegate is already set"

    .line 90
    .line 91
    invoke-direct {p1, v0}, Ljava/lang/AssertionError;-><init>(Ljava/lang/Object;)V

    .line 92
    .line 93
    .line 94
    throw p1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 95
    :cond_5
    :goto_1
    if-eqz v3, :cond_6

    .line 96
    .line 97
    invoke-virtual {v1}, Ljava/lang/ThreadLocal;->remove()V

    .line 98
    .line 99
    .line 100
    :cond_6
    if-eqz v6, :cond_8

    .line 101
    .line 102
    if-eqz v3, :cond_7

    .line 103
    .line 104
    invoke-virtual {v0, v2}, Ljava/util/concurrent/ConcurrentHashMap;->putAll(Ljava/util/Map;)V

    .line 105
    .line 106
    .line 107
    :cond_7
    return-object v6

    .line 108
    :cond_8
    new-instance v0, Ljava/lang/IllegalArgumentException;

    .line 109
    .line 110
    new-instance v1, Ljava/lang/StringBuilder;

    .line 111
    .line 112
    const-string v2, "GSON (2.11.0) cannot handle "

    .line 113
    .line 114
    invoke-direct {v1, v2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 115
    .line 116
    .line 117
    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 118
    .line 119
    .line 120
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 121
    .line 122
    .line 123
    move-result-object p1

    .line 124
    invoke-direct {v0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 125
    .line 126
    .line 127
    throw v0

    .line 128
    :goto_2
    if-eqz v3, :cond_9

    .line 129
    .line 130
    invoke-virtual {v1}, Ljava/lang/ThreadLocal;->remove()V

    .line 131
    .line 132
    .line 133
    :cond_9
    throw p1
.end method

.method public final c(Lcom/github/catvod/spider/merge/a/f;Ljava/lang/Class;Lcom/github/catvod/spider/merge/l/a;)V
    .locals 5

    .line 1
    const-string v0, "AssertionError (GSON 2.11.0): "

    .line 2
    .line 3
    new-instance v1, Lcom/github/catvod/spider/merge/k/a;

    .line 4
    .line 5
    invoke-direct {v1, p2}, Lcom/github/catvod/spider/merge/k/a;-><init>(Ljava/lang/reflect/Type;)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {p0, v1}, Lcom/github/catvod/spider/merge/e/n;->b(Lcom/github/catvod/spider/merge/k/a;)Lcom/github/catvod/spider/merge/e/z;

    .line 9
    .line 10
    .line 11
    move-result-object p2

    .line 12
    iget v1, p3, Lcom/github/catvod/spider/merge/l/a;->h:I

    .line 13
    .line 14
    const/4 v2, 0x2

    .line 15
    if-ne v1, v2, :cond_0

    .line 16
    .line 17
    const/4 v2, 0x1

    .line 18
    iput v2, p3, Lcom/github/catvod/spider/merge/l/a;->h:I

    .line 19
    .line 20
    :cond_0
    iget-boolean v2, p3, Lcom/github/catvod/spider/merge/l/a;->i:Z

    .line 21
    .line 22
    iget-boolean v3, p3, Lcom/github/catvod/spider/merge/l/a;->k:Z

    .line 23
    .line 24
    iget-boolean v4, p0, Lcom/github/catvod/spider/merge/e/n;->i:Z

    .line 25
    .line 26
    iput-boolean v4, p3, Lcom/github/catvod/spider/merge/l/a;->i:Z

    .line 27
    .line 28
    const/4 v4, 0x0

    .line 29
    iput-boolean v4, p3, Lcom/github/catvod/spider/merge/l/a;->k:Z

    .line 30
    .line 31
    :try_start_0
    invoke-virtual {p2, p3, p1}, Lcom/github/catvod/spider/merge/e/z;->a(Lcom/github/catvod/spider/merge/l/a;Ljava/lang/Object;)V
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_1
    .catch Ljava/lang/AssertionError; {:try_start_0 .. :try_end_0} :catch_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 32
    .line 33
    .line 34
    invoke-virtual {p3, v1}, Lcom/github/catvod/spider/merge/l/a;->l(I)V

    .line 35
    .line 36
    .line 37
    iput-boolean v2, p3, Lcom/github/catvod/spider/merge/l/a;->i:Z

    .line 38
    .line 39
    iput-boolean v3, p3, Lcom/github/catvod/spider/merge/l/a;->k:Z

    .line 40
    .line 41
    return-void

    .line 42
    :catchall_0
    move-exception p1

    .line 43
    goto :goto_0

    .line 44
    :catch_0
    move-exception p1

    .line 45
    :try_start_1
    new-instance p2, Ljava/lang/AssertionError;

    .line 46
    .line 47
    new-instance v4, Ljava/lang/StringBuilder;

    .line 48
    .line 49
    invoke-direct {v4, v0}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 50
    .line 51
    .line 52
    invoke-virtual {p1}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    .line 53
    .line 54
    .line 55
    move-result-object v0

    .line 56
    invoke-virtual {v4, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 57
    .line 58
    .line 59
    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 60
    .line 61
    .line 62
    move-result-object v0

    .line 63
    invoke-direct {p2, v0, p1}, Ljava/lang/AssertionError;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    .line 64
    .line 65
    .line 66
    throw p2

    .line 67
    :catch_1
    move-exception p1

    .line 68
    new-instance p2, Lcom/github/catvod/spider/merge/e/q;

    .line 69
    .line 70
    invoke-direct {p2, p1}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/Throwable;)V

    .line 71
    .line 72
    .line 73
    throw p2
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 74
    :goto_0
    invoke-virtual {p3, v1}, Lcom/github/catvod/spider/merge/l/a;->l(I)V

    .line 75
    .line 76
    .line 77
    iput-boolean v2, p3, Lcom/github/catvod/spider/merge/l/a;->i:Z

    .line 78
    .line 79
    iput-boolean v3, p3, Lcom/github/catvod/spider/merge/l/a;->k:Z

    .line 80
    .line 81
    throw p1
.end method

.method public final toString()Ljava/lang/String;
    .locals 2

    .line 1
    new-instance v0, Ljava/lang/StringBuilder;

    .line 2
    .line 3
    const-string v1, "{serializeNulls:false,factories:"

    .line 4
    .line 5
    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    iget-object v1, p0, Lcom/github/catvod/spider/merge/e/n;->e:Ljava/util/List;

    .line 9
    .line 10
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 11
    .line 12
    .line 13
    const-string v1, ",instanceCreators:"

    .line 14
    .line 15
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 16
    .line 17
    .line 18
    iget-object v1, p0, Lcom/github/catvod/spider/merge/e/n;->c:Lcom/github/catvod/spider/merge/g/h;

    .line 19
    .line 20
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 21
    .line 22
    .line 23
    const-string v1, "}"

    .line 24
    .line 25
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 26
    .line 27
    .line 28
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 29
    .line 30
    .line 31
    move-result-object v0

    .line 32
    return-object v0
.end method
