.class public Lcom/github/catvod/spider/merge/a/f;
.super Ljava/lang/Object;
.source "SourceFile"


# instance fields
.field private a:Ljava/util/List;
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "class"
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/github/catvod/spider/merge/a/b;",
            ">;"
        }
    .end annotation
.end field

.field private b:Ljava/util/List;
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "list"
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lcom/github/catvod/spider/merge/a/h;",
            ">;"
        }
    .end annotation
.end field

.field private c:Ljava/util/LinkedHashMap;
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "filters"
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/LinkedHashMap<",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Ljava/lang/Object;",
            ">;>;"
        }
    .end annotation
.end field

.field private d:Ljava/lang/String;
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "header"
    .end annotation
.end field

.field private e:Ljava/lang/String;
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "format"
    .end annotation
.end field

.field private f:Ljava/util/List;
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "danmaku"
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field

.field private g:Ljava/lang/String;
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "click"
    .end annotation
.end field

.field private h:Ljava/lang/String;
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "msg"
    .end annotation
.end field

.field private i:Ljava/lang/Object;
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "url"
    .end annotation
.end field

.field private j:Ljava/util/List;
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "subs"
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field

.field private k:I
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "parse"
    .end annotation
.end field

.field private l:I
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "jx"
    .end annotation
.end field

.field private m:Ljava/lang/Integer;
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "page"
    .end annotation
.end field

.field private n:Ljava/lang/Integer;
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "pagecount"
    .end annotation
.end field

.field private o:Ljava/lang/Integer;
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "limit"
    .end annotation
.end field

.field private p:Ljava/lang/Integer;
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "total"
    .end annotation
.end field


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static a(Ljava/util/ArrayList;Ljava/util/ArrayList;)Ljava/lang/String;
    .locals 1

    .line 1
    new-instance v0, Lcom/github/catvod/spider/merge/a/f;

    .line 2
    .line 3
    invoke-direct {v0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    iput-object p0, v0, Lcom/github/catvod/spider/merge/a/f;->a:Ljava/util/List;

    .line 7
    .line 8
    iput-object p1, v0, Lcom/github/catvod/spider/merge/a/f;->b:Ljava/util/List;

    .line 9
    .line 10
    invoke-virtual {v0}, Lcom/github/catvod/spider/merge/a/f;->toString()Ljava/lang/String;

    .line 11
    .line 12
    .line 13
    move-result-object p0

    .line 14
    return-object p0
.end method


# virtual methods
.method public final b()V
    .locals 1

    .line 1
    const-string v0, ""

    .line 2
    .line 3
    iput-object v0, p0, Lcom/github/catvod/spider/merge/a/f;->i:Ljava/lang/Object;

    .line 4
    .line 5
    return-void
.end method

.method public final c(Ljava/util/ArrayList;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/github/catvod/spider/merge/a/f;->b:Ljava/util/List;

    .line 2
    .line 3
    return-void
.end method

.method public final toString()Ljava/lang/String;
    .locals 23

    .line 1
    const/4 v0, 0x0

    .line 2
    new-instance v1, Lcom/github/catvod/spider/merge/e/n;

    .line 3
    .line 4
    sget-object v2, Lcom/github/catvod/spider/merge/g/j;->c:Lcom/github/catvod/spider/merge/g/j;

    .line 5
    .line 6
    sget-object v3, Lcom/github/catvod/spider/merge/e/n;->u:Lcom/github/catvod/spider/merge/e/a;

    .line 7
    .line 8
    sget-object v4, Ljava/util/Collections;->EMPTY_MAP:Ljava/util/Map;

    .line 9
    .line 10
    sget-object v11, Ljava/util/Collections;->EMPTY_LIST:Ljava/util/List;

    .line 11
    .line 12
    sget-object v14, Lcom/github/catvod/spider/merge/e/n;->v:Lcom/github/catvod/spider/merge/e/u;

    .line 13
    .line 14
    sget-object v15, Lcom/github/catvod/spider/merge/e/n;->w:Lcom/github/catvod/spider/merge/e/v;

    .line 15
    .line 16
    sget-object v6, Lcom/github/catvod/spider/merge/e/n;->t:Lcom/github/catvod/spider/merge/e/i;

    .line 17
    .line 18
    const/4 v5, 0x1

    .line 19
    const/4 v7, 0x1

    .line 20
    const/4 v8, 0x1

    .line 21
    const/4 v9, 0x2

    .line 22
    const/4 v10, 0x2

    .line 23
    move-object v12, v11

    .line 24
    move-object v13, v11

    .line 25
    move-object/from16 v16, v11

    .line 26
    .line 27
    invoke-direct/range {v1 .. v16}, Lcom/github/catvod/spider/merge/e/n;-><init>(Lcom/github/catvod/spider/merge/g/j;Lcom/github/catvod/spider/merge/e/h;Ljava/util/Map;ZLcom/github/catvod/spider/merge/e/i;ZIIILjava/util/List;Ljava/util/List;Ljava/util/List;Lcom/github/catvod/spider/merge/e/y;Lcom/github/catvod/spider/merge/e/y;Ljava/util/List;)V

    .line 28
    .line 29
    .line 30
    new-instance v2, Ljava/util/HashMap;

    .line 31
    .line 32
    invoke-direct {v2}, Ljava/util/HashMap;-><init>()V

    .line 33
    .line 34
    .line 35
    new-instance v3, Ljava/util/ArrayList;

    .line 36
    .line 37
    invoke-direct {v3}, Ljava/util/ArrayList;-><init>()V

    .line 38
    .line 39
    .line 40
    new-instance v4, Ljava/util/ArrayList;

    .line 41
    .line 42
    invoke-direct {v4}, Ljava/util/ArrayList;-><init>()V

    .line 43
    .line 44
    .line 45
    new-instance v5, Ljava/util/ArrayDeque;

    .line 46
    .line 47
    invoke-direct {v5}, Ljava/util/ArrayDeque;-><init>()V

    .line 48
    .line 49
    .line 50
    iget-object v6, v1, Lcom/github/catvod/spider/merge/e/n;->h:Ljava/util/Map;

    .line 51
    .line 52
    invoke-virtual {v2, v6}, Ljava/util/HashMap;->putAll(Ljava/util/Map;)V

    .line 53
    .line 54
    .line 55
    iget-object v6, v1, Lcom/github/catvod/spider/merge/e/n;->n:Ljava/util/List;

    .line 56
    .line 57
    invoke-virtual {v3, v6}, Ljava/util/ArrayList;->addAll(Ljava/util/Collection;)Z

    .line 58
    .line 59
    .line 60
    iget-object v6, v1, Lcom/github/catvod/spider/merge/e/n;->o:Ljava/util/List;

    .line 61
    .line 62
    invoke-virtual {v4, v6}, Ljava/util/ArrayList;->addAll(Ljava/util/Collection;)Z

    .line 63
    .line 64
    .line 65
    iget-object v6, v1, Lcom/github/catvod/spider/merge/e/n;->r:Ljava/util/List;

    .line 66
    .line 67
    invoke-virtual {v5, v6}, Ljava/util/ArrayDeque;->addAll(Ljava/util/Collection;)Z

    .line 68
    .line 69
    .line 70
    new-instance v6, Ljava/util/ArrayList;

    .line 71
    .line 72
    invoke-virtual {v3}, Ljava/util/ArrayList;->size()I

    .line 73
    .line 74
    .line 75
    move-result v7

    .line 76
    invoke-virtual {v4}, Ljava/util/ArrayList;->size()I

    .line 77
    .line 78
    .line 79
    move-result v8

    .line 80
    add-int/2addr v8, v7

    .line 81
    add-int/lit8 v8, v8, 0x3

    .line 82
    .line 83
    invoke-direct {v6, v8}, Ljava/util/ArrayList;-><init>(I)V

    .line 84
    .line 85
    .line 86
    invoke-virtual {v6, v3}, Ljava/util/ArrayList;->addAll(Ljava/util/Collection;)Z

    .line 87
    .line 88
    .line 89
    invoke-static {v6}, Ljava/util/Collections;->reverse(Ljava/util/List;)V

    .line 90
    .line 91
    .line 92
    new-instance v7, Ljava/util/ArrayList;

    .line 93
    .line 94
    invoke-direct {v7, v4}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    .line 95
    .line 96
    .line 97
    invoke-static {v7}, Ljava/util/Collections;->reverse(Ljava/util/List;)V

    .line 98
    .line 99
    .line 100
    invoke-virtual {v6, v7}, Ljava/util/ArrayList;->addAll(Ljava/util/Collection;)Z

    .line 101
    .line 102
    .line 103
    sget-boolean v7, Lcom/github/catvod/spider/merge/j/h;->a:Z

    .line 104
    .line 105
    sget-object v8, Lcom/github/catvod/spider/merge/h/g;->b:Lcom/github/catvod/spider/merge/h/f;

    .line 106
    .line 107
    const/4 v9, 0x2

    .line 108
    iget v15, v1, Lcom/github/catvod/spider/merge/e/n;->l:I

    .line 109
    .line 110
    iget v10, v1, Lcom/github/catvod/spider/merge/e/n;->m:I

    .line 111
    .line 112
    if-ne v15, v9, :cond_0

    .line 113
    .line 114
    if-eq v10, v9, :cond_2

    .line 115
    .line 116
    :cond_0
    new-instance v11, Lcom/github/catvod/spider/merge/h/h;

    .line 117
    .line 118
    invoke-direct {v11, v8, v15, v10}, Lcom/github/catvod/spider/merge/h/h;-><init>(Lcom/github/catvod/spider/merge/h/g;II)V

    .line 119
    .line 120
    .line 121
    sget-object v8, Lcom/github/catvod/spider/merge/h/e0;->a:Lcom/github/catvod/spider/merge/h/U;

    .line 122
    .line 123
    new-instance v8, Lcom/github/catvod/spider/merge/h/U;

    .line 124
    .line 125
    const-class v12, Ljava/util/Date;

    .line 126
    .line 127
    invoke-direct {v8, v12, v11, v0}, Lcom/github/catvod/spider/merge/h/U;-><init>(Ljava/lang/Class;Lcom/github/catvod/spider/merge/e/z;I)V

    .line 128
    .line 129
    .line 130
    if-eqz v7, :cond_1

    .line 131
    .line 132
    sget-object v11, Lcom/github/catvod/spider/merge/j/h;->c:Lcom/github/catvod/spider/merge/j/g;

    .line 133
    .line 134
    invoke-virtual {v11}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 135
    .line 136
    .line 137
    new-instance v12, Lcom/github/catvod/spider/merge/h/h;

    .line 138
    .line 139
    invoke-direct {v12, v11, v15, v10}, Lcom/github/catvod/spider/merge/h/h;-><init>(Lcom/github/catvod/spider/merge/h/g;II)V

    .line 140
    .line 141
    .line 142
    new-instance v13, Lcom/github/catvod/spider/merge/h/U;

    .line 143
    .line 144
    iget-object v11, v11, Lcom/github/catvod/spider/merge/h/g;->a:Ljava/lang/Class;

    .line 145
    .line 146
    invoke-direct {v13, v11, v12, v0}, Lcom/github/catvod/spider/merge/h/U;-><init>(Ljava/lang/Class;Lcom/github/catvod/spider/merge/e/z;I)V

    .line 147
    .line 148
    .line 149
    sget-object v11, Lcom/github/catvod/spider/merge/j/h;->b:Lcom/github/catvod/spider/merge/j/g;

    .line 150
    .line 151
    invoke-virtual {v11}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 152
    .line 153
    .line 154
    new-instance v12, Lcom/github/catvod/spider/merge/h/h;

    .line 155
    .line 156
    invoke-direct {v12, v11, v15, v10}, Lcom/github/catvod/spider/merge/h/h;-><init>(Lcom/github/catvod/spider/merge/h/g;II)V

    .line 157
    .line 158
    .line 159
    new-instance v14, Lcom/github/catvod/spider/merge/h/U;

    .line 160
    .line 161
    iget-object v11, v11, Lcom/github/catvod/spider/merge/h/g;->a:Ljava/lang/Class;

    .line 162
    .line 163
    invoke-direct {v14, v11, v12, v0}, Lcom/github/catvod/spider/merge/h/U;-><init>(Ljava/lang/Class;Lcom/github/catvod/spider/merge/e/z;I)V

    .line 164
    .line 165
    .line 166
    goto :goto_0

    .line 167
    :cond_1
    const/4 v13, 0x0

    .line 168
    move-object v14, v13

    .line 169
    :goto_0
    invoke-virtual {v6, v8}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 170
    .line 171
    .line 172
    if-eqz v7, :cond_2

    .line 173
    .line 174
    invoke-virtual {v6, v13}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 175
    .line 176
    .line 177
    invoke-virtual {v6, v14}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 178
    .line 179
    .line 180
    :cond_2
    new-instance v7, Lcom/github/catvod/spider/merge/e/n;

    .line 181
    .line 182
    move/from16 v16, v10

    .line 183
    .line 184
    new-instance v10, Ljava/util/HashMap;

    .line 185
    .line 186
    invoke-direct {v10, v2}, Ljava/util/HashMap;-><init>(Ljava/util/Map;)V

    .line 187
    .line 188
    .line 189
    new-instance v2, Ljava/util/ArrayList;

    .line 190
    .line 191
    invoke-direct {v2, v3}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    .line 192
    .line 193
    .line 194
    new-instance v3, Ljava/util/ArrayList;

    .line 195
    .line 196
    invoke-direct {v3, v4}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    .line 197
    .line 198
    .line 199
    new-instance v4, Ljava/util/ArrayList;

    .line 200
    .line 201
    invoke-direct {v4, v5}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    .line 202
    .line 203
    .line 204
    iget-object v5, v1, Lcom/github/catvod/spider/merge/e/n;->p:Lcom/github/catvod/spider/merge/e/y;

    .line 205
    .line 206
    iget-object v8, v1, Lcom/github/catvod/spider/merge/e/n;->q:Lcom/github/catvod/spider/merge/e/y;

    .line 207
    .line 208
    move-object/from16 v21, v8

    .line 209
    .line 210
    iget-object v8, v1, Lcom/github/catvod/spider/merge/e/n;->f:Lcom/github/catvod/spider/merge/g/j;

    .line 211
    .line 212
    const/4 v11, 0x2

    .line 213
    iget-object v9, v1, Lcom/github/catvod/spider/merge/e/n;->g:Lcom/github/catvod/spider/merge/e/h;

    .line 214
    .line 215
    const/4 v12, 0x2

    .line 216
    const/4 v11, 0x0

    .line 217
    const/4 v13, 0x2

    .line 218
    iget-object v12, v1, Lcom/github/catvod/spider/merge/e/n;->j:Lcom/github/catvod/spider/merge/e/i;

    .line 219
    .line 220
    const/4 v14, 0x2

    .line 221
    iget-boolean v13, v1, Lcom/github/catvod/spider/merge/e/n;->k:Z

    .line 222
    .line 223
    iget v1, v1, Lcom/github/catvod/spider/merge/e/n;->s:I

    .line 224
    .line 225
    move v14, v1

    .line 226
    move-object/from16 v17, v2

    .line 227
    .line 228
    move-object/from16 v18, v3

    .line 229
    .line 230
    move-object/from16 v22, v4

    .line 231
    .line 232
    move-object/from16 v20, v5

    .line 233
    .line 234
    move-object/from16 v19, v6

    .line 235
    .line 236
    const/4 v1, 0x2

    .line 237
    invoke-direct/range {v7 .. v22}, Lcom/github/catvod/spider/merge/e/n;-><init>(Lcom/github/catvod/spider/merge/g/j;Lcom/github/catvod/spider/merge/e/h;Ljava/util/Map;ZLcom/github/catvod/spider/merge/e/i;ZIIILjava/util/List;Ljava/util/List;Ljava/util/List;Lcom/github/catvod/spider/merge/e/y;Lcom/github/catvod/spider/merge/e/y;Ljava/util/List;)V

    .line 238
    .line 239
    .line 240
    invoke-virtual/range {p0 .. p0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 241
    .line 242
    .line 243
    move-result-object v2

    .line 244
    new-instance v3, Ljava/io/StringWriter;

    .line 245
    .line 246
    invoke-direct {v3}, Ljava/io/StringWriter;-><init>()V

    .line 247
    .line 248
    .line 249
    :try_start_0
    new-instance v4, Lcom/github/catvod/spider/merge/l/a;

    .line 250
    .line 251
    invoke-direct {v4, v3}, Lcom/github/catvod/spider/merge/l/a;-><init>(Ljava/io/Writer;)V

    .line 252
    .line 253
    .line 254
    iget-object v5, v7, Lcom/github/catvod/spider/merge/e/n;->j:Lcom/github/catvod/spider/merge/e/i;

    .line 255
    .line 256
    invoke-virtual {v4, v5}, Lcom/github/catvod/spider/merge/l/a;->k(Lcom/github/catvod/spider/merge/e/i;)V

    .line 257
    .line 258
    .line 259
    iget-boolean v5, v7, Lcom/github/catvod/spider/merge/e/n;->i:Z

    .line 260
    .line 261
    iput-boolean v5, v4, Lcom/github/catvod/spider/merge/l/a;->i:Z

    .line 262
    .line 263
    invoke-virtual {v4, v1}, Lcom/github/catvod/spider/merge/l/a;->l(I)V

    .line 264
    .line 265
    .line 266
    iput-boolean v0, v4, Lcom/github/catvod/spider/merge/l/a;->k:Z
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_1

    .line 267
    .line 268
    move-object/from16 v1, p0

    .line 269
    .line 270
    :try_start_1
    invoke-virtual {v7, v1, v2, v4}, Lcom/github/catvod/spider/merge/e/n;->c(Lcom/github/catvod/spider/merge/a/f;Ljava/lang/Class;Lcom/github/catvod/spider/merge/l/a;)V
    :try_end_1
    .catch Ljava/io/IOException; {:try_start_1 .. :try_end_1} :catch_0

    .line 271
    .line 272
    .line 273
    invoke-virtual {v3}, Ljava/io/StringWriter;->toString()Ljava/lang/String;

    .line 274
    .line 275
    .line 276
    move-result-object v0

    .line 277
    return-object v0

    .line 278
    :catch_0
    move-exception v0

    .line 279
    goto :goto_1

    .line 280
    :catch_1
    move-exception v0

    .line 281
    move-object/from16 v1, p0

    .line 282
    .line 283
    :goto_1
    new-instance v2, Lcom/github/catvod/spider/merge/e/q;

    .line 284
    .line 285
    invoke-direct {v2, v0}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/Throwable;)V

    .line 286
    .line 287
    .line 288
    throw v2
.end method
