.class public final enum Lcom/github/catvod/spider/merge/u/a;
.super Ljava/lang/Enum;
.source "SourceFile"


# static fields
.field public static final enum a:Lcom/github/catvod/spider/merge/u/a;


# direct methods
.method static constructor <clinit>()V
    .locals 7

    .line 1
    const/4 v0, 0x2

    .line 2
    const/4 v1, 0x1

    .line 3
    const/4 v2, 0x0

    .line 4
    new-instance v3, Lcom/github/catvod/spider/merge/u/a;

    .line 5
    .line 6
    const-string v4, "COROUTINE_SUSPENDED"

    .line 7
    .line 8
    invoke-direct {v3, v4, v2}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 9
    .line 10
    .line 11
    sput-object v3, Lcom/github/catvod/spider/merge/u/a;->a:Lcom/github/catvod/spider/merge/u/a;

    .line 12
    .line 13
    new-instance v4, Lcom/github/catvod/spider/merge/u/a;

    .line 14
    .line 15
    const-string v5, "UNDECIDED"

    .line 16
    .line 17
    invoke-direct {v4, v5, v1}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 18
    .line 19
    .line 20
    new-instance v5, Lcom/github/catvod/spider/merge/u/a;

    .line 21
    .line 22
    const-string v6, "RESUMED"

    .line 23
    .line 24
    invoke-direct {v5, v6, v0}, Ljava/lang/Enum;-><init>(Ljava/lang/String;I)V

    .line 25
    .line 26
    .line 27
    const/4 v6, 0x3

    .line 28
    new-array v6, v6, [Lcom/github/catvod/spider/merge/u/a;

    .line 29
    .line 30
    aput-object v3, v6, v2

    .line 31
    .line 32
    aput-object v4, v6, v1

    .line 33
    .line 34
    aput-object v5, v6, v0

    .line 35
    .line 36
    invoke-static {v6}, Lcom/github/catvod/spider/merge/A/a;->i([Ljava/lang/Enum;)Lcom/github/catvod/spider/merge/w/b;

    .line 37
    .line 38
    .line 39
    return-void
.end method
