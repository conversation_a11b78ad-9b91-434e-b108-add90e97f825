.class public final Lokio/-GzipSinkExtensions;
.super Ljava/lang/Object;
.source "SourceFile"


# direct methods
.method public static final gzip(<PERSON><PERSON>/Sink;)Lokio/GzipSink;
    .locals 1

    .line 1
    const-string v0, "<this>"

    .line 2
    .line 3
    invoke-static {p0, v0}, Lcom/github/catvod/spider/merge/C/f;->e(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    new-instance v0, Lokio/GzipSink;

    .line 7
    .line 8
    invoke-direct {v0, p0}, Lokio/GzipSink;-><init>(<PERSON><PERSON>/Sink;)V

    .line 9
    .line 10
    .line 11
    return-object v0
.end method
