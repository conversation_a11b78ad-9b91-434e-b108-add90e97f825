.class public abstract Lcom/github/catvod/spider/merge/v/c;
.super Lcom/github/catvod/spider/merge/v/a;
.source "SourceFile"


# instance fields
.field private final _context:Lcom/github/catvod/spider/merge/t/b;

.field private transient intercepted:Lcom/github/catvod/spider/merge/t/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/github/catvod/spider/merge/t/a;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lcom/github/catvod/spider/merge/t/a;Lcom/github/catvod/spider/merge/t/b;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lcom/github/catvod/spider/merge/v/a;-><init>(Lcom/github/catvod/spider/merge/t/a;)V

    .line 2
    .line 3
    .line 4
    iput-object p2, p0, Lcom/github/catvod/spider/merge/v/c;->_context:Lcom/github/catvod/spider/merge/t/b;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public getContext()Lcom/github/catvod/spider/merge/t/b;
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/github/catvod/spider/merge/v/c;->_context:Lcom/github/catvod/spider/merge/t/b;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/github/catvod/spider/merge/C/f;->b(Ljava/lang/Object;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public final intercepted()Lcom/github/catvod/spider/merge/t/a;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lcom/github/catvod/spider/merge/t/a;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/github/catvod/spider/merge/v/c;->intercepted:Lcom/github/catvod/spider/merge/t/a;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {p0}, Lcom/github/catvod/spider/merge/v/c;->getContext()Lcom/github/catvod/spider/merge/t/b;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    check-cast v0, Lcom/github/catvod/spider/merge/t/c;

    .line 10
    .line 11
    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 12
    .line 13
    .line 14
    iput-object p0, p0, Lcom/github/catvod/spider/merge/v/c;->intercepted:Lcom/github/catvod/spider/merge/t/a;

    .line 15
    .line 16
    return-object p0

    .line 17
    :cond_0
    return-object v0
.end method

.method public releaseIntercepted()V
    .locals 1

    .line 1
    iget-object v0, p0, Lcom/github/catvod/spider/merge/v/c;->intercepted:Lcom/github/catvod/spider/merge/t/a;

    .line 2
    .line 3
    if-eqz v0, :cond_1

    .line 4
    .line 5
    if-ne v0, p0, :cond_0

    .line 6
    .line 7
    goto :goto_0

    .line 8
    :cond_0
    invoke-virtual {p0}, Lcom/github/catvod/spider/merge/v/c;->getContext()Lcom/github/catvod/spider/merge/t/b;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    check-cast v0, Lcom/github/catvod/spider/merge/t/c;

    .line 13
    .line 14
    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 15
    .line 16
    .line 17
    const/4 v0, 0x0

    .line 18
    invoke-static {v0}, Lcom/github/catvod/spider/merge/C/f;->b(Ljava/lang/Object;)V

    .line 19
    .line 20
    .line 21
    throw v0

    .line 22
    :cond_1
    :goto_0
    sget-object v0, Lcom/github/catvod/spider/merge/v/b;->a:Lcom/github/catvod/spider/merge/v/b;

    .line 23
    .line 24
    iput-object v0, p0, Lcom/github/catvod/spider/merge/v/c;->intercepted:Lcom/github/catvod/spider/merge/t/a;

    .line 25
    .line 26
    return-void
.end method
