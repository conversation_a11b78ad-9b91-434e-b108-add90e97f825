.class public final Lcom/github/catvod/spider/merge/H/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/util/Iterator;
.implements Lcom/github/catvod/spider/merge/D/a;


# instance fields
.field public a:I

.field public b:I

.field public c:I

.field public d:Lcom/github/catvod/spider/merge/E/f;

.field public final synthetic e:Lcom/github/catvod/spider/merge/G/h;


# direct methods
.method public constructor <init>(Lcom/github/catvod/spider/merge/G/h;)V
    .locals 3

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lcom/github/catvod/spider/merge/H/b;->e:Lcom/github/catvod/spider/merge/G/h;

    .line 5
    .line 6
    const/4 v0, -0x1

    .line 7
    iput v0, p0, Lcom/github/catvod/spider/merge/H/b;->a:I

    .line 8
    .line 9
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 10
    .line 11
    .line 12
    iget-object p1, p1, Lcom/github/catvod/spider/merge/G/h;->c:Ljava/lang/Object;

    .line 13
    .line 14
    check-cast p1, Ljava/lang/CharSequence;

    .line 15
    .line 16
    invoke-interface {p1}, Ljava/lang/CharSequence;->length()I

    .line 17
    .line 18
    .line 19
    move-result p1

    .line 20
    if-ltz p1, :cond_1

    .line 21
    .line 22
    if-gez p1, :cond_0

    .line 23
    .line 24
    goto :goto_0

    .line 25
    :cond_0
    const/4 p1, 0x0

    .line 26
    :goto_0
    iput p1, p0, Lcom/github/catvod/spider/merge/H/b;->b:I

    .line 27
    .line 28
    iput p1, p0, Lcom/github/catvod/spider/merge/H/b;->c:I

    .line 29
    .line 30
    return-void

    .line 31
    :cond_1
    new-instance v0, Ljava/lang/IllegalArgumentException;

    .line 32
    .line 33
    new-instance v1, Ljava/lang/StringBuilder;

    .line 34
    .line 35
    const-string v2, "Cannot coerce value to an empty range: maximum "

    .line 36
    .line 37
    invoke-direct {v1, v2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 38
    .line 39
    .line 40
    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 41
    .line 42
    .line 43
    const-string p1, " is less than minimum 0."

    .line 44
    .line 45
    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 46
    .line 47
    .line 48
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    invoke-direct {v0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 53
    .line 54
    .line 55
    throw v0
.end method


# virtual methods
.method public final a()V
    .locals 7

    .line 1
    iget v0, p0, Lcom/github/catvod/spider/merge/H/b;->c:I

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    if-gez v0, :cond_0

    .line 5
    .line 6
    iput v1, p0, Lcom/github/catvod/spider/merge/H/b;->a:I

    .line 7
    .line 8
    const/4 v0, 0x0

    .line 9
    iput-object v0, p0, Lcom/github/catvod/spider/merge/H/b;->d:Lcom/github/catvod/spider/merge/E/f;

    .line 10
    .line 11
    return-void

    .line 12
    :cond_0
    iget-object v2, p0, Lcom/github/catvod/spider/merge/H/b;->e:Lcom/github/catvod/spider/merge/G/h;

    .line 13
    .line 14
    invoke-virtual {v2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 15
    .line 16
    .line 17
    iget-object v3, v2, Lcom/github/catvod/spider/merge/G/h;->c:Ljava/lang/Object;

    .line 18
    .line 19
    check-cast v3, Ljava/lang/CharSequence;

    .line 20
    .line 21
    invoke-interface {v3}, Ljava/lang/CharSequence;->length()I

    .line 22
    .line 23
    .line 24
    move-result v4

    .line 25
    const/4 v5, -0x1

    .line 26
    const/4 v6, 0x1

    .line 27
    if-le v0, v4, :cond_1

    .line 28
    .line 29
    new-instance v0, Lcom/github/catvod/spider/merge/E/f;

    .line 30
    .line 31
    iget v1, p0, Lcom/github/catvod/spider/merge/H/b;->b:I

    .line 32
    .line 33
    invoke-static {v3}, Lcom/github/catvod/spider/merge/H/j;->H(Ljava/lang/CharSequence;)I

    .line 34
    .line 35
    .line 36
    move-result v2

    .line 37
    invoke-direct {v0, v1, v2, v6}, Lcom/github/catvod/spider/merge/E/d;-><init>(III)V

    .line 38
    .line 39
    .line 40
    iput-object v0, p0, Lcom/github/catvod/spider/merge/H/b;->d:Lcom/github/catvod/spider/merge/E/f;

    .line 41
    .line 42
    iput v5, p0, Lcom/github/catvod/spider/merge/H/b;->c:I

    .line 43
    .line 44
    goto :goto_0

    .line 45
    :cond_1
    iget-object v0, v2, Lcom/github/catvod/spider/merge/G/h;->b:Lcom/github/catvod/spider/merge/C/g;

    .line 46
    .line 47
    iget v2, p0, Lcom/github/catvod/spider/merge/H/b;->c:I

    .line 48
    .line 49
    invoke-static {v2}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 50
    .line 51
    .line 52
    move-result-object v2

    .line 53
    invoke-interface {v0, v3, v2}, Lcom/github/catvod/spider/merge/B/p;->invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 54
    .line 55
    .line 56
    move-result-object v0

    .line 57
    check-cast v0, Lcom/github/catvod/spider/merge/p/c;

    .line 58
    .line 59
    if-nez v0, :cond_2

    .line 60
    .line 61
    new-instance v0, Lcom/github/catvod/spider/merge/E/f;

    .line 62
    .line 63
    iget v1, p0, Lcom/github/catvod/spider/merge/H/b;->b:I

    .line 64
    .line 65
    invoke-static {v3}, Lcom/github/catvod/spider/merge/H/j;->H(Ljava/lang/CharSequence;)I

    .line 66
    .line 67
    .line 68
    move-result v2

    .line 69
    invoke-direct {v0, v1, v2, v6}, Lcom/github/catvod/spider/merge/E/d;-><init>(III)V

    .line 70
    .line 71
    .line 72
    iput-object v0, p0, Lcom/github/catvod/spider/merge/H/b;->d:Lcom/github/catvod/spider/merge/E/f;

    .line 73
    .line 74
    iput v5, p0, Lcom/github/catvod/spider/merge/H/b;->c:I

    .line 75
    .line 76
    goto :goto_0

    .line 77
    :cond_2
    iget-object v2, v0, Lcom/github/catvod/spider/merge/p/c;->a:Ljava/lang/Object;

    .line 78
    .line 79
    check-cast v2, Ljava/lang/Number;

    .line 80
    .line 81
    invoke-virtual {v2}, Ljava/lang/Number;->intValue()I

    .line 82
    .line 83
    .line 84
    move-result v2

    .line 85
    iget-object v0, v0, Lcom/github/catvod/spider/merge/p/c;->b:Ljava/lang/Object;

    .line 86
    .line 87
    check-cast v0, Ljava/lang/Number;

    .line 88
    .line 89
    invoke-virtual {v0}, Ljava/lang/Number;->intValue()I

    .line 90
    .line 91
    .line 92
    move-result v0

    .line 93
    iget v3, p0, Lcom/github/catvod/spider/merge/H/b;->b:I

    .line 94
    .line 95
    invoke-static {v3, v2}, Lcom/github/catvod/spider/merge/A/a;->t(II)Lcom/github/catvod/spider/merge/E/f;

    .line 96
    .line 97
    .line 98
    move-result-object v3

    .line 99
    iput-object v3, p0, Lcom/github/catvod/spider/merge/H/b;->d:Lcom/github/catvod/spider/merge/E/f;

    .line 100
    .line 101
    add-int/2addr v2, v0

    .line 102
    iput v2, p0, Lcom/github/catvod/spider/merge/H/b;->b:I

    .line 103
    .line 104
    if-nez v0, :cond_3

    .line 105
    .line 106
    const/4 v1, 0x1

    .line 107
    :cond_3
    add-int/2addr v2, v1

    .line 108
    iput v2, p0, Lcom/github/catvod/spider/merge/H/b;->c:I

    .line 109
    .line 110
    :goto_0
    iput v6, p0, Lcom/github/catvod/spider/merge/H/b;->a:I

    .line 111
    .line 112
    return-void
.end method

.method public final hasNext()Z
    .locals 2

    .line 1
    iget v0, p0, Lcom/github/catvod/spider/merge/H/b;->a:I

    .line 2
    .line 3
    const/4 v1, -0x1

    .line 4
    if-ne v0, v1, :cond_0

    .line 5
    .line 6
    invoke-virtual {p0}, Lcom/github/catvod/spider/merge/H/b;->a()V

    .line 7
    .line 8
    .line 9
    :cond_0
    iget v0, p0, Lcom/github/catvod/spider/merge/H/b;->a:I

    .line 10
    .line 11
    const/4 v1, 0x1

    .line 12
    if-ne v0, v1, :cond_1

    .line 13
    .line 14
    return v1

    .line 15
    :cond_1
    const/4 v0, 0x0

    .line 16
    return v0
.end method

.method public final next()Ljava/lang/Object;
    .locals 3

    .line 1
    iget v0, p0, Lcom/github/catvod/spider/merge/H/b;->a:I

    .line 2
    .line 3
    const/4 v1, -0x1

    .line 4
    if-ne v0, v1, :cond_0

    .line 5
    .line 6
    invoke-virtual {p0}, Lcom/github/catvod/spider/merge/H/b;->a()V

    .line 7
    .line 8
    .line 9
    :cond_0
    iget v0, p0, Lcom/github/catvod/spider/merge/H/b;->a:I

    .line 10
    .line 11
    if-eqz v0, :cond_1

    .line 12
    .line 13
    iget-object v0, p0, Lcom/github/catvod/spider/merge/H/b;->d:Lcom/github/catvod/spider/merge/E/f;

    .line 14
    .line 15
    const-string v2, "null cannot be cast to non-null type kotlin.ranges.IntRange"

    .line 16
    .line 17
    invoke-static {v0, v2}, Lcom/github/catvod/spider/merge/C/f;->c(Ljava/lang/Object;Ljava/lang/String;)V

    .line 18
    .line 19
    .line 20
    const/4 v2, 0x0

    .line 21
    iput-object v2, p0, Lcom/github/catvod/spider/merge/H/b;->d:Lcom/github/catvod/spider/merge/E/f;

    .line 22
    .line 23
    iput v1, p0, Lcom/github/catvod/spider/merge/H/b;->a:I

    .line 24
    .line 25
    return-object v0

    .line 26
    :cond_1
    new-instance v0, Ljava/util/NoSuchElementException;

    .line 27
    .line 28
    invoke-direct {v0}, Ljava/util/NoSuchElementException;-><init>()V

    .line 29
    .line 30
    .line 31
    throw v0
.end method

.method public final remove()V
    .locals 2

    .line 1
    new-instance v0, Ljava/lang/UnsupportedOperationException;

    .line 2
    .line 3
    const-string v1, "Operation is not supported for read-only collection"

    .line 4
    .line 5
    invoke-direct {v0, v1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    throw v0
.end method
