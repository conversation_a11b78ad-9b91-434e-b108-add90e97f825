.class public final Lcom/github/catvod/spider/merge/h/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/github/catvod/spider/merge/e/A;


# instance fields
.field public final synthetic a:I

.field public final b:Lcom/github/catvod/spider/merge/g/h;


# direct methods
.method public synthetic constructor <init>(Lcom/github/catvod/spider/merge/g/h;I)V
    .locals 0

    .line 1
    iput p2, p0, Lcom/github/catvod/spider/merge/h/d;->a:I

    iput-object p1, p0, Lcom/github/catvod/spider/merge/h/d;->b:Lcom/github/catvod/spider/merge/g/h;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Lcom/github/catvod/spider/merge/e/n;Lcom/github/catvod/spider/merge/k/a;)Lcom/github/catvod/spider/merge/e/z;
    .locals 13

    .line 1
    iget-object v0, p0, Lcom/github/catvod/spider/merge/h/d;->b:Lcom/github/catvod/spider/merge/g/h;

    .line 2
    .line 3
    const-class v1, Ljava/lang/Object;

    .line 4
    .line 5
    const/4 v2, 0x2

    .line 6
    const/4 v3, 0x0

    .line 7
    const/4 v4, 0x1

    .line 8
    const/4 v5, 0x0

    .line 9
    iget v6, p0, Lcom/github/catvod/spider/merge/h/d;->a:I

    .line 10
    .line 11
    packed-switch v6, :pswitch_data_0

    .line 12
    .line 13
    .line 14
    const-class v6, Ljava/util/Map;

    .line 15
    .line 16
    iget-object v7, p2, Lcom/github/catvod/spider/merge/k/a;->a:Ljava/lang/Class;

    .line 17
    .line 18
    invoke-virtual {v6, v7}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    .line 19
    .line 20
    .line 21
    move-result v8

    .line 22
    if-nez v8, :cond_0

    .line 23
    .line 24
    goto/16 :goto_4

    .line 25
    .line 26
    :cond_0
    const-class v3, Ljava/util/Properties;

    .line 27
    .line 28
    iget-object v8, p2, Lcom/github/catvod/spider/merge/k/a;->b:Ljava/lang/reflect/Type;

    .line 29
    .line 30
    if-ne v8, v3, :cond_1

    .line 31
    .line 32
    new-array v1, v2, [Ljava/lang/reflect/Type;

    .line 33
    .line 34
    const-class v2, Ljava/lang/String;

    .line 35
    .line 36
    aput-object v2, v1, v5

    .line 37
    .line 38
    aput-object v2, v1, v4

    .line 39
    .line 40
    goto :goto_0

    .line 41
    :cond_1
    instance-of v3, v8, Ljava/lang/reflect/WildcardType;

    .line 42
    .line 43
    if-eqz v3, :cond_2

    .line 44
    .line 45
    check-cast v8, Ljava/lang/reflect/WildcardType;

    .line 46
    .line 47
    invoke-interface {v8}, Ljava/lang/reflect/WildcardType;->getUpperBounds()[Ljava/lang/reflect/Type;

    .line 48
    .line 49
    .line 50
    move-result-object v3

    .line 51
    aget-object v8, v3, v5

    .line 52
    .line 53
    :cond_2
    invoke-virtual {v6, v7}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    .line 54
    .line 55
    .line 56
    move-result v3

    .line 57
    invoke-static {v3}, Lcom/github/catvod/spider/merge/g/d;->b(Z)V

    .line 58
    .line 59
    .line 60
    invoke-static {v8, v7, v6}, Lcom/github/catvod/spider/merge/g/d;->f(Ljava/lang/reflect/Type;Ljava/lang/Class;Ljava/lang/Class;)Ljava/lang/reflect/Type;

    .line 61
    .line 62
    .line 63
    move-result-object v3

    .line 64
    new-instance v6, Ljava/util/HashMap;

    .line 65
    .line 66
    invoke-direct {v6}, Ljava/util/HashMap;-><init>()V

    .line 67
    .line 68
    .line 69
    invoke-static {v8, v7, v3, v6}, Lcom/github/catvod/spider/merge/g/d;->h(Ljava/lang/reflect/Type;Ljava/lang/Class;Ljava/lang/reflect/Type;Ljava/util/HashMap;)Ljava/lang/reflect/Type;

    .line 70
    .line 71
    .line 72
    move-result-object v3

    .line 73
    instance-of v6, v3, Ljava/lang/reflect/ParameterizedType;

    .line 74
    .line 75
    if-eqz v6, :cond_3

    .line 76
    .line 77
    check-cast v3, Ljava/lang/reflect/ParameterizedType;

    .line 78
    .line 79
    invoke-interface {v3}, Ljava/lang/reflect/ParameterizedType;->getActualTypeArguments()[Ljava/lang/reflect/Type;

    .line 80
    .line 81
    .line 82
    move-result-object v1

    .line 83
    goto :goto_0

    .line 84
    :cond_3
    new-array v2, v2, [Ljava/lang/reflect/Type;

    .line 85
    .line 86
    aput-object v1, v2, v5

    .line 87
    .line 88
    aput-object v1, v2, v4

    .line 89
    .line 90
    move-object v1, v2

    .line 91
    :goto_0
    aget-object v2, v1, v5

    .line 92
    .line 93
    sget-object v3, Ljava/lang/Boolean;->TYPE:Ljava/lang/Class;

    .line 94
    .line 95
    if-eq v2, v3, :cond_5

    .line 96
    .line 97
    const-class v3, Ljava/lang/Boolean;

    .line 98
    .line 99
    if-ne v2, v3, :cond_4

    .line 100
    .line 101
    goto :goto_2

    .line 102
    :cond_4
    new-instance v3, Lcom/github/catvod/spider/merge/k/a;

    .line 103
    .line 104
    invoke-direct {v3, v2}, Lcom/github/catvod/spider/merge/k/a;-><init>(Ljava/lang/reflect/Type;)V

    .line 105
    .line 106
    .line 107
    invoke-virtual {p1, v3}, Lcom/github/catvod/spider/merge/e/n;->b(Lcom/github/catvod/spider/merge/k/a;)Lcom/github/catvod/spider/merge/e/z;

    .line 108
    .line 109
    .line 110
    move-result-object v2

    .line 111
    :goto_1
    move-object v10, v2

    .line 112
    goto :goto_3

    .line 113
    :cond_5
    :goto_2
    sget-object v2, Lcom/github/catvod/spider/merge/h/e0;->c:Lcom/github/catvod/spider/merge/h/X;

    .line 114
    .line 115
    goto :goto_1

    .line 116
    :goto_3
    aget-object v2, v1, v4

    .line 117
    .line 118
    new-instance v3, Lcom/github/catvod/spider/merge/k/a;

    .line 119
    .line 120
    invoke-direct {v3, v2}, Lcom/github/catvod/spider/merge/k/a;-><init>(Ljava/lang/reflect/Type;)V

    .line 121
    .line 122
    .line 123
    invoke-virtual {p1, v3}, Lcom/github/catvod/spider/merge/e/n;->b(Lcom/github/catvod/spider/merge/k/a;)Lcom/github/catvod/spider/merge/e/z;

    .line 124
    .line 125
    .line 126
    move-result-object v12

    .line 127
    invoke-virtual {v0, p2}, Lcom/github/catvod/spider/merge/g/h;->b(Lcom/github/catvod/spider/merge/k/a;)Lcom/github/catvod/spider/merge/g/r;

    .line 128
    .line 129
    .line 130
    new-instance v6, Lcom/github/catvod/spider/merge/h/k;

    .line 131
    .line 132
    aget-object v9, v1, v5

    .line 133
    .line 134
    aget-object v11, v1, v4

    .line 135
    .line 136
    move-object v7, p0

    .line 137
    move-object v8, p1

    .line 138
    invoke-direct/range {v6 .. v12}, Lcom/github/catvod/spider/merge/h/k;-><init>(Lcom/github/catvod/spider/merge/h/d;Lcom/github/catvod/spider/merge/e/n;Ljava/lang/reflect/Type;Lcom/github/catvod/spider/merge/e/z;Ljava/lang/reflect/Type;Lcom/github/catvod/spider/merge/e/z;)V

    .line 139
    .line 140
    .line 141
    move-object v3, v6

    .line 142
    :goto_4
    return-object v3

    .line 143
    :pswitch_0
    move-object v8, p1

    .line 144
    const-class p1, Ljava/util/Collection;

    .line 145
    .line 146
    iget-object v2, p2, Lcom/github/catvod/spider/merge/k/a;->a:Ljava/lang/Class;

    .line 147
    .line 148
    invoke-virtual {p1, v2}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    .line 149
    .line 150
    .line 151
    move-result v4

    .line 152
    if-nez v4, :cond_6

    .line 153
    .line 154
    goto :goto_5

    .line 155
    :cond_6
    iget-object v3, p2, Lcom/github/catvod/spider/merge/k/a;->b:Ljava/lang/reflect/Type;

    .line 156
    .line 157
    instance-of v4, v3, Ljava/lang/reflect/WildcardType;

    .line 158
    .line 159
    if-eqz v4, :cond_7

    .line 160
    .line 161
    check-cast v3, Ljava/lang/reflect/WildcardType;

    .line 162
    .line 163
    invoke-interface {v3}, Ljava/lang/reflect/WildcardType;->getUpperBounds()[Ljava/lang/reflect/Type;

    .line 164
    .line 165
    .line 166
    move-result-object v3

    .line 167
    aget-object v3, v3, v5

    .line 168
    .line 169
    :cond_7
    invoke-virtual {p1, v2}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    .line 170
    .line 171
    .line 172
    move-result v4

    .line 173
    invoke-static {v4}, Lcom/github/catvod/spider/merge/g/d;->b(Z)V

    .line 174
    .line 175
    .line 176
    invoke-static {v3, v2, p1}, Lcom/github/catvod/spider/merge/g/d;->f(Ljava/lang/reflect/Type;Ljava/lang/Class;Ljava/lang/Class;)Ljava/lang/reflect/Type;

    .line 177
    .line 178
    .line 179
    move-result-object p1

    .line 180
    new-instance v4, Ljava/util/HashMap;

    .line 181
    .line 182
    invoke-direct {v4}, Ljava/util/HashMap;-><init>()V

    .line 183
    .line 184
    .line 185
    invoke-static {v3, v2, p1, v4}, Lcom/github/catvod/spider/merge/g/d;->h(Ljava/lang/reflect/Type;Ljava/lang/Class;Ljava/lang/reflect/Type;Ljava/util/HashMap;)Ljava/lang/reflect/Type;

    .line 186
    .line 187
    .line 188
    move-result-object p1

    .line 189
    instance-of v2, p1, Ljava/lang/reflect/ParameterizedType;

    .line 190
    .line 191
    if-eqz v2, :cond_8

    .line 192
    .line 193
    check-cast p1, Ljava/lang/reflect/ParameterizedType;

    .line 194
    .line 195
    invoke-interface {p1}, Ljava/lang/reflect/ParameterizedType;->getActualTypeArguments()[Ljava/lang/reflect/Type;

    .line 196
    .line 197
    .line 198
    move-result-object p1

    .line 199
    aget-object v1, p1, v5

    .line 200
    .line 201
    :cond_8
    new-instance p1, Lcom/github/catvod/spider/merge/k/a;

    .line 202
    .line 203
    invoke-direct {p1, v1}, Lcom/github/catvod/spider/merge/k/a;-><init>(Ljava/lang/reflect/Type;)V

    .line 204
    .line 205
    .line 206
    invoke-virtual {v8, p1}, Lcom/github/catvod/spider/merge/e/n;->b(Lcom/github/catvod/spider/merge/k/a;)Lcom/github/catvod/spider/merge/e/z;

    .line 207
    .line 208
    .line 209
    move-result-object p1

    .line 210
    invoke-virtual {v0, p2}, Lcom/github/catvod/spider/merge/g/h;->b(Lcom/github/catvod/spider/merge/k/a;)Lcom/github/catvod/spider/merge/g/r;

    .line 211
    .line 212
    .line 213
    new-instance v3, Lcom/github/catvod/spider/merge/h/c;

    .line 214
    .line 215
    invoke-direct {v3, v8, p1, v1}, Lcom/github/catvod/spider/merge/h/c;-><init>(Lcom/github/catvod/spider/merge/e/n;Lcom/github/catvod/spider/merge/e/z;Ljava/lang/reflect/Type;)V

    .line 216
    .line 217
    .line 218
    :goto_5
    return-object v3

    .line 219
    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_0
    .end packed-switch
.end method
