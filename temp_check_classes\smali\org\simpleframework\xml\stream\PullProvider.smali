.class Lorg/simpleframework/xml/stream/PullProvider;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lorg/simpleframework/xml/stream/Provider;


# instance fields
.field private final factory:Lcom/github/catvod/spider/merge/P/c;


# direct methods
.method public constructor <init>()V
    .locals 11

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    sget-object v0, Lcom/github/catvod/spider/merge/P/c;->d:Ljava/lang/Class;

    .line 5
    .line 6
    :try_start_0
    const-string v1, "/META-INF/services/org.xmlpull.v1.XmlPullParserFactory"

    .line 7
    .line 8
    invoke-virtual {v0, v1}, Ljava/lang/Class;->getResourceAsStream(Ljava/lang/String;)Ljava/io/InputStream;

    .line 9
    .line 10
    .line 11
    move-result-object v0

    .line 12
    if-eqz v0, :cond_9

    .line 13
    .line 14
    new-instance v1, Lja<PERSON>/lang/StringBuffer;

    .line 15
    .line 16
    invoke-direct {v1}, Ljava/lang/StringBuffer;-><init>()V

    .line 17
    .line 18
    .line 19
    :cond_0
    :goto_0
    invoke-virtual {v0}, Ljava/io/InputStream;->read()I

    .line 20
    .line 21
    .line 22
    move-result v2

    .line 23
    if-gez v2, :cond_8

    .line 24
    .line 25
    invoke-virtual {v0}, Ljava/io/InputStream;->close()V

    .line 26
    .line 27
    .line 28
    invoke-virtual {v1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    .line 29
    .line 30
    .line 31
    move-result-object v0
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_2

    .line 32
    new-instance v1, Ljava/lang/StringBuffer;

    .line 33
    .line 34
    const-string v2, "resource /META-INF/services/org.xmlpull.v1.XmlPullParserFactory that contained \'"

    .line 35
    .line 36
    invoke-direct {v1, v2}, Ljava/lang/StringBuffer;-><init>(Ljava/lang/String;)V

    .line 37
    .line 38
    .line 39
    invoke-virtual {v1, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    .line 40
    .line 41
    .line 42
    const-string v2, "\'"

    .line 43
    .line 44
    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    .line 45
    .line 46
    .line 47
    invoke-virtual {v1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    .line 48
    .line 49
    .line 50
    move-result-object v1

    .line 51
    new-instance v2, Ljava/util/Vector;

    .line 52
    .line 53
    invoke-direct {v2}, Ljava/util/Vector;-><init>()V

    .line 54
    .line 55
    .line 56
    new-instance v3, Ljava/util/Vector;

    .line 57
    .line 58
    invoke-direct {v3}, Ljava/util/Vector;-><init>()V

    .line 59
    .line 60
    .line 61
    const/4 v3, 0x0

    .line 62
    const/4 v4, 0x0

    .line 63
    move-object v6, v4

    .line 64
    const/4 v5, 0x0

    .line 65
    :goto_1
    invoke-virtual {v0}, Ljava/lang/String;->length()I

    .line 66
    .line 67
    .line 68
    move-result v7

    .line 69
    const/4 v8, 0x1

    .line 70
    if-lt v5, v7, :cond_2

    .line 71
    .line 72
    if-nez v6, :cond_1

    .line 73
    .line 74
    new-instance v6, Lcom/github/catvod/spider/merge/P/c;

    .line 75
    .line 76
    invoke-direct {v6}, Lcom/github/catvod/spider/merge/P/c;-><init>()V

    .line 77
    .line 78
    .line 79
    :cond_1
    iput-object v2, v6, Lcom/github/catvod/spider/merge/P/c;->a:Ljava/util/Vector;

    .line 80
    .line 81
    iput-object v1, v6, Lcom/github/catvod/spider/merge/P/c;->b:Ljava/lang/String;

    .line 82
    .line 83
    iput-object v6, p0, Lorg/simpleframework/xml/stream/PullProvider;->factory:Lcom/github/catvod/spider/merge/P/c;

    .line 84
    .line 85
    iget-object v0, v6, Lcom/github/catvod/spider/merge/P/c;->c:Ljava/util/Hashtable;

    .line 86
    .line 87
    new-instance v1, Ljava/lang/Boolean;

    .line 88
    .line 89
    invoke-direct {v1, v8}, Ljava/lang/Boolean;-><init>(Z)V

    .line 90
    .line 91
    .line 92
    const-string v2, "http://xmlpull.org/v1/doc/features.html#process-namespaces"

    .line 93
    .line 94
    invoke-virtual {v0, v2, v1}, Ljava/util/Hashtable;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 95
    .line 96
    .line 97
    return-void

    .line 98
    :cond_2
    const/16 v7, 0x2c

    .line 99
    .line 100
    invoke-virtual {v0, v7, v5}, Ljava/lang/String;->indexOf(II)I

    .line 101
    .line 102
    .line 103
    move-result v7

    .line 104
    const/4 v9, -0x1

    .line 105
    if-ne v7, v9, :cond_3

    .line 106
    .line 107
    invoke-virtual {v0}, Ljava/lang/String;->length()I

    .line 108
    .line 109
    .line 110
    move-result v7

    .line 111
    :cond_3
    invoke-virtual {v0, v5, v7}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    .line 112
    .line 113
    .line 114
    move-result-object v5

    .line 115
    :try_start_1
    invoke-static {v5}, Ljava/lang/Class;->forName(Ljava/lang/String;)Ljava/lang/Class;

    .line 116
    .line 117
    .line 118
    move-result-object v9
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_1

    .line 119
    :try_start_2
    invoke-virtual {v9}, Ljava/lang/Class;->newInstance()Ljava/lang/Object;

    .line 120
    .line 121
    .line 122
    move-result-object v10
    :try_end_2
    .catch Ljava/lang/Exception; {:try_start_2 .. :try_end_2} :catch_0

    .line 123
    goto :goto_3

    .line 124
    :catch_0
    nop

    .line 125
    goto :goto_2

    .line 126
    :catch_1
    nop

    .line 127
    move-object v9, v4

    .line 128
    :goto_2
    move-object v10, v4

    .line 129
    :goto_3
    if-eqz v9, :cond_7

    .line 130
    .line 131
    instance-of v9, v10, Lcom/github/catvod/spider/merge/P/c;

    .line 132
    .line 133
    if-eqz v9, :cond_4

    .line 134
    .line 135
    if-nez v6, :cond_5

    .line 136
    .line 137
    move-object v6, v10

    .line 138
    check-cast v6, Lcom/github/catvod/spider/merge/P/c;

    .line 139
    .line 140
    goto :goto_4

    .line 141
    :cond_4
    const/4 v8, 0x0

    .line 142
    :cond_5
    :goto_4
    if-eqz v8, :cond_6

    .line 143
    .line 144
    goto :goto_5

    .line 145
    :cond_6
    new-instance v0, Lcom/github/catvod/spider/merge/P/b;

    .line 146
    .line 147
    new-instance v1, Ljava/lang/StringBuffer;

    .line 148
    .line 149
    const-string v2, "incompatible class: "

    .line 150
    .line 151
    invoke-direct {v1, v2}, Ljava/lang/StringBuffer;-><init>(Ljava/lang/String;)V

    .line 152
    .line 153
    .line 154
    invoke-virtual {v1, v5}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    .line 155
    .line 156
    .line 157
    invoke-virtual {v1}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    .line 158
    .line 159
    .line 160
    move-result-object v1

    .line 161
    invoke-direct {v0, v1}, Ljava/lang/Exception;-><init>(Ljava/lang/String;)V

    .line 162
    .line 163
    .line 164
    throw v0

    .line 165
    :cond_7
    :goto_5
    add-int/lit8 v5, v7, 0x1

    .line 166
    .line 167
    goto :goto_1

    .line 168
    :catch_2
    move-exception v0

    .line 169
    goto :goto_6

    .line 170
    :cond_8
    const/16 v3, 0x20

    .line 171
    .line 172
    if-le v2, v3, :cond_0

    .line 173
    .line 174
    int-to-char v2, v2

    .line 175
    :try_start_3
    invoke-virtual {v1, v2}, Ljava/lang/StringBuffer;->append(C)Ljava/lang/StringBuffer;

    .line 176
    .line 177
    .line 178
    goto/16 :goto_0

    .line 179
    .line 180
    :cond_9
    new-instance v0, Lcom/github/catvod/spider/merge/P/b;

    .line 181
    .line 182
    const-string v1, "resource not found: /META-INF/services/org.xmlpull.v1.XmlPullParserFactory make sure that parser implementing XmlPull API is available"

    .line 183
    .line 184
    invoke-direct {v0, v1}, Ljava/lang/Exception;-><init>(Ljava/lang/String;)V

    .line 185
    .line 186
    .line 187
    throw v0
    :try_end_3
    .catch Ljava/lang/Exception; {:try_start_3 .. :try_end_3} :catch_2

    .line 188
    :goto_6
    new-instance v1, Lcom/github/catvod/spider/merge/P/b;

    .line 189
    .line 190
    new-instance v2, Ljava/lang/StringBuffer;

    .line 191
    .line 192
    const-string v3, ""

    .line 193
    .line 194
    invoke-direct {v2, v3}, Ljava/lang/StringBuffer;-><init>(Ljava/lang/String;)V

    .line 195
    .line 196
    .line 197
    new-instance v3, Ljava/lang/StringBuffer;

    .line 198
    .line 199
    const-string v4, "caused by: "

    .line 200
    .line 201
    invoke-direct {v3, v4}, Ljava/lang/StringBuffer;-><init>(Ljava/lang/String;)V

    .line 202
    .line 203
    .line 204
    invoke-virtual {v3, v0}, Ljava/lang/StringBuffer;->append(Ljava/lang/Object;)Ljava/lang/StringBuffer;

    .line 205
    .line 206
    .line 207
    invoke-virtual {v3}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    .line 208
    .line 209
    .line 210
    move-result-object v3

    .line 211
    invoke-virtual {v2, v3}, Ljava/lang/StringBuffer;->append(Ljava/lang/String;)Ljava/lang/StringBuffer;

    .line 212
    .line 213
    .line 214
    invoke-virtual {v2}, Ljava/lang/StringBuffer;->toString()Ljava/lang/String;

    .line 215
    .line 216
    .line 217
    move-result-object v2

    .line 218
    invoke-direct {v1, v2}, Ljava/lang/Exception;-><init>(Ljava/lang/String;)V

    .line 219
    .line 220
    .line 221
    iput-object v0, v1, Lcom/github/catvod/spider/merge/P/b;->a:Ljava/lang/Exception;

    .line 222
    .line 223
    throw v1
.end method


# virtual methods
.method public provide(Ljava/io/InputStream;)Lorg/simpleframework/xml/stream/EventReader;
    .locals 1

    .line 1
    iget-object v0, p0, Lorg/simpleframework/xml/stream/PullProvider;->factory:Lcom/github/catvod/spider/merge/P/c;

    invoke-virtual {v0}, Lcom/github/catvod/spider/merge/P/c;->a()V

    const/4 v0, 0x0

    if-nez p1, :cond_0

    .line 2
    new-instance p1, Lorg/simpleframework/xml/stream/PullReader;

    invoke-direct {p1, v0}, Lorg/simpleframework/xml/stream/PullReader;-><init>(Lcom/github/catvod/spider/merge/P/a;)V

    return-object p1

    .line 3
    :cond_0
    throw v0
.end method

.method public provide(Ljava/io/Reader;)Lorg/simpleframework/xml/stream/EventReader;
    .locals 1

    .line 4
    iget-object v0, p0, Lorg/simpleframework/xml/stream/PullProvider;->factory:Lcom/github/catvod/spider/merge/P/c;

    invoke-virtual {v0}, Lcom/github/catvod/spider/merge/P/c;->a()V

    const/4 v0, 0x0

    if-nez p1, :cond_0

    .line 5
    new-instance p1, Lorg/simpleframework/xml/stream/PullReader;

    invoke-direct {p1, v0}, Lorg/simpleframework/xml/stream/PullReader;-><init>(Lcom/github/catvod/spider/merge/P/a;)V

    return-object p1

    .line 6
    :cond_0
    throw v0
.end method
