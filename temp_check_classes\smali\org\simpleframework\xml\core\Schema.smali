.class interface abstract Lorg/simpleframework/xml/core/Schema;
.super Ljava/lang/Object;
.source "SourceFile"


# virtual methods
.method public abstract getCaller()Lorg/simpleframework/xml/core/Caller;
.end method

.method public abstract getDecorator()Lorg/simpleframework/xml/core/Decorator;
.end method

.method public abstract getInstantiator()Lorg/simpleframework/xml/core/Instantiator;
.end method

.method public abstract getRevision()Lorg/simpleframework/xml/Version;
.end method

.method public abstract getSection()Lorg/simpleframework/xml/core/Section;
.end method

.method public abstract getText()Lorg/simpleframework/xml/core/Label;
.end method

.method public abstract getVersion()Lorg/simpleframework/xml/core/Label;
.end method

.method public abstract isPrimitive()Z
.end method
