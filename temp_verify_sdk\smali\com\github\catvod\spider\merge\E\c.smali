.class public final Lcom/github/catvod/spider/merge/E/c;
.super Lcom/github/catvod/spider/merge/E/a;
.source "SourceFile"


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 1
    new-instance v0, Lcom/github/catvod/spider/merge/E/c;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    const/4 v2, 0x0

    .line 5
    invoke-direct {v0, v1, v2}, Lcom/github/catvod/spider/merge/E/a;-><init>(CC)V

    .line 6
    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public final equals(Ljava/lang/Object;)Z
    .locals 4

    .line 1
    instance-of v0, p1, Lcom/github/catvod/spider/merge/E/c;

    .line 2
    .line 3
    if-eqz v0, :cond_1

    .line 4
    .line 5
    iget-char v0, p0, Lcom/github/catvod/spider/merge/E/a;->a:C

    .line 6
    .line 7
    iget-char v1, p0, <PERSON>com/github/catvod/spider/merge/E/a;->b:C

    .line 8
    .line 9
    invoke-static {v0, v1}, Lcom/github/catvod/spider/merge/C/f;->f(II)I

    .line 10
    .line 11
    .line 12
    move-result v2

    .line 13
    if-lez v2, :cond_0

    .line 14
    .line 15
    move-object v2, p1

    .line 16
    check-cast v2, Lcom/github/catvod/spider/merge/E/c;

    .line 17
    .line 18
    iget-char v3, v2, Lcom/github/catvod/spider/merge/E/a;->a:C

    .line 19
    .line 20
    iget-char v2, v2, Lcom/github/catvod/spider/merge/E/a;->b:C

    .line 21
    .line 22
    invoke-static {v3, v2}, Lcom/github/catvod/spider/merge/C/f;->f(II)I

    .line 23
    .line 24
    .line 25
    move-result v2

    .line 26
    if-lez v2, :cond_0

    .line 27
    .line 28
    goto :goto_0

    .line 29
    :cond_0
    check-cast p1, Lcom/github/catvod/spider/merge/E/c;

    .line 30
    .line 31
    iget-char v2, p1, Lcom/github/catvod/spider/merge/E/a;->a:C

    .line 32
    .line 33
    if-ne v0, v2, :cond_1

    .line 34
    .line 35
    iget-char p1, p1, Lcom/github/catvod/spider/merge/E/a;->b:C

    .line 36
    .line 37
    if-ne v1, p1, :cond_1

    .line 38
    .line 39
    :goto_0
    const/4 p1, 0x1

    .line 40
    return p1

    .line 41
    :cond_1
    const/4 p1, 0x0

    .line 42
    return p1
.end method

.method public final hashCode()I
    .locals 3

    .line 1
    iget-char v0, p0, Lcom/github/catvod/spider/merge/E/a;->a:C

    .line 2
    .line 3
    iget-char v1, p0, Lcom/github/catvod/spider/merge/E/a;->b:C

    .line 4
    .line 5
    invoke-static {v0, v1}, Lcom/github/catvod/spider/merge/C/f;->f(II)I

    .line 6
    .line 7
    .line 8
    move-result v2

    .line 9
    if-lez v2, :cond_0

    .line 10
    .line 11
    const/4 v0, -0x1

    .line 12
    return v0

    .line 13
    :cond_0
    mul-int/lit8 v0, v0, 0x1f

    .line 14
    .line 15
    add-int/2addr v0, v1

    .line 16
    return v0
.end method

.method public final toString()Ljava/lang/String;
    .locals 2

    .line 1
    new-instance v0, Ljava/lang/StringBuilder;

    .line 2
    .line 3
    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 4
    .line 5
    .line 6
    iget-char v1, p0, Lcom/github/catvod/spider/merge/E/a;->a:C

    .line 7
    .line 8
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 9
    .line 10
    .line 11
    const-string v1, ".."

    .line 12
    .line 13
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 14
    .line 15
    .line 16
    iget-char v1, p0, Lcom/github/catvod/spider/merge/E/a;->b:C

    .line 17
    .line 18
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 19
    .line 20
    .line 21
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    return-object v0
.end method
