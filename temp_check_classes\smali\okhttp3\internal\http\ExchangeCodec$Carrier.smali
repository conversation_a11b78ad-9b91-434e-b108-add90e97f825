.class public interface abstract Lokhttp3/internal/http/ExchangeCodec$Carrier;
.super Ljava/lang/Object;
.source "SourceFile"


# virtual methods
.method public abstract cancel()V
.end method

.method public abstract getRoute()Lokhttp3/Route;
.end method

.method public abstract noNewExchanges()V
.end method

.method public abstract trackFailure(Lokhttp3/internal/connection/RealCall;Ljava/io/IOException;)V
.end method
