.class public Lorg/slf4j/event/EventConstants;
.super Ljava/lang/Object;
.source "SourceFile"


# static fields
.field public static final DEBUG_INT:I = 0xa

.field public static final ERROR_INT:I = 0x28

.field public static final INFO_INT:I = 0x14

.field public static final NA_SUBST:Ljava/lang/String; = "NA/SubstituteLogger"

.field public static final TRACE_INT:I = 0x0

.field public static final WARN_INT:I = 0x1e


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
