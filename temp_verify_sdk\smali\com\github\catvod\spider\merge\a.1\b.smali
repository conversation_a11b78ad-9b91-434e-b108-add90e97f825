.class public Lcom/github/catvod/spider/merge/a/b;
.super Ljava/lang/Object;
.source "SourceFile"


# instance fields
.field private a:Ljava/lang/String;
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "type_id"
    .end annotation
.end field

.field private b:Ljava/lang/String;
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "type_name"
    .end annotation
.end field

.field private c:Ljava/lang/String;
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "type_flag"
    .end annotation
.end field


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    const-string v0, "1"

    .line 5
    .line 6
    iput-object v0, p0, Lcom/github/catvod/spider/merge/a/b;->a:Ljava/lang/String;

    .line 7
    .line 8
    const-string v0, "\u5f39\u5e55\u8bbe\u7f6e"

    .line 9
    .line 10
    iput-object v0, p0, Lcom/github/catvod/spider/merge/a/b;->b:Ljava/lang/String;

    .line 11
    .line 12
    const/4 v0, 0x0

    .line 13
    iput-object v0, p0, Lcom/github/catvod/spider/merge/a/b;->c:Ljava/lang/String;

    .line 14
    .line 15
    return-void
.end method


# virtual methods
.method public final equals(Ljava/lang/Object;)Z
    .locals 1

    .line 1
    if-ne p0, p1, :cond_0

    .line 2
    .line 3
    const/4 p1, 0x1

    .line 4
    return p1

    .line 5
    :cond_0
    instance-of v0, p1, Lcom/github/catvod/spider/merge/a/b;

    .line 6
    .line 7
    if-nez v0, :cond_1

    .line 8
    .line 9
    const/4 p1, 0x0

    .line 10
    return p1

    .line 11
    :cond_1
    check-cast p1, Lcom/github/catvod/spider/merge/a/b;

    .line 12
    .line 13
    iget-object v0, p0, Lcom/github/catvod/spider/merge/a/b;->a:Ljava/lang/String;

    .line 14
    .line 15
    iget-object p1, p1, Lcom/github/catvod/spider/merge/a/b;->a:Ljava/lang/String;

    .line 16
    .line 17
    invoke-virtual {v0, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 18
    .line 19
    .line 20
    move-result p1

    .line 21
    return p1
.end method
