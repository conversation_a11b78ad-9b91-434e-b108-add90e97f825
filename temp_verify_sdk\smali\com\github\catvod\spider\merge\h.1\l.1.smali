.class public final Lcom/github/catvod/spider/merge/h/l;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/github/catvod/spider/merge/e/A;


# instance fields
.field public final synthetic a:I

.field public final synthetic b:Lcom/github/catvod/spider/merge/e/z;


# direct methods
.method public synthetic constructor <init>(Lcom/github/catvod/spider/merge/e/z;I)V
    .locals 0

    .line 1
    iput p2, p0, Lcom/github/catvod/spider/merge/h/l;->a:I

    iput-object p1, p0, Lcom/github/catvod/spider/merge/h/l;->b:Lcom/github/catvod/spider/merge/e/z;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Lcom/github/catvod/spider/merge/e/n;Lcom/github/catvod/spider/merge/k/a;)Lcom/github/catvod/spider/merge/e/z;
    .locals 0

    .line 1
    iget p1, p0, Lcom/github/catvod/spider/merge/h/l;->a:I

    .line 2
    .line 3
    packed-switch p1, :pswitch_data_0

    .line 4
    .line 5
    .line 6
    const-class p1, Ljava/util/Calendar;

    .line 7
    .line 8
    iget-object p2, p2, Lcom/github/catvod/spider/merge/k/a;->a:Ljava/lang/Class;

    .line 9
    .line 10
    if-eq p2, p1, :cond_1

    .line 11
    .line 12
    const-class p1, Ljava/util/GregorianCalendar;

    .line 13
    .line 14
    if-ne p2, p1, :cond_0

    .line 15
    .line 16
    goto :goto_0

    .line 17
    :cond_0
    const/4 p1, 0x0

    .line 18
    goto :goto_1

    .line 19
    :cond_1
    :goto_0
    iget-object p1, p0, Lcom/github/catvod/spider/merge/h/l;->b:Lcom/github/catvod/spider/merge/e/z;

    .line 20
    .line 21
    check-cast p1, Lcom/github/catvod/spider/merge/h/O;

    .line 22
    .line 23
    :goto_1
    return-object p1

    .line 24
    :pswitch_0
    const-class p1, Ljava/lang/Number;

    .line 25
    .line 26
    iget-object p2, p2, Lcom/github/catvod/spider/merge/k/a;->a:Ljava/lang/Class;

    .line 27
    .line 28
    if-ne p2, p1, :cond_2

    .line 29
    .line 30
    iget-object p1, p0, Lcom/github/catvod/spider/merge/h/l;->b:Lcom/github/catvod/spider/merge/e/z;

    .line 31
    .line 32
    check-cast p1, Lcom/github/catvod/spider/merge/h/m;

    .line 33
    .line 34
    goto :goto_2

    .line 35
    :cond_2
    const/4 p1, 0x0

    .line 36
    :goto_2
    return-object p1

    .line 37
    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_0
    .end packed-switch
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    .line 1
    iget v0, p0, Lcom/github/catvod/spider/merge/h/l;->a:I

    .line 2
    .line 3
    packed-switch v0, :pswitch_data_0

    .line 4
    .line 5
    .line 6
    invoke-super {p0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    return-object v0

    .line 11
    :pswitch_0
    new-instance v0, Ljava/lang/StringBuilder;

    .line 12
    .line 13
    const-string v1, "Factory[type="

    .line 14
    .line 15
    invoke-direct {v0, v1}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 16
    .line 17
    .line 18
    const-class v1, Ljava/util/Calendar;

    .line 19
    .line 20
    invoke-virtual {v1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    .line 21
    .line 22
    .line 23
    move-result-object v1

    .line 24
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 25
    .line 26
    .line 27
    const-string v1, "+"

    .line 28
    .line 29
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 30
    .line 31
    .line 32
    const-class v1, Ljava/util/GregorianCalendar;

    .line 33
    .line 34
    invoke-virtual {v1}, Ljava/lang/Class;->getName()Ljava/lang/String;

    .line 35
    .line 36
    .line 37
    move-result-object v1

    .line 38
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 39
    .line 40
    .line 41
    const-string v1, ",adapter="

    .line 42
    .line 43
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 44
    .line 45
    .line 46
    iget-object v1, p0, Lcom/github/catvod/spider/merge/h/l;->b:Lcom/github/catvod/spider/merge/e/z;

    .line 47
    .line 48
    check-cast v1, Lcom/github/catvod/spider/merge/h/O;

    .line 49
    .line 50
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 51
    .line 52
    .line 53
    const-string v1, "]"

    .line 54
    .line 55
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 56
    .line 57
    .line 58
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 59
    .line 60
    .line 61
    move-result-object v0

    .line 62
    return-object v0

    .line 63
    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_0
    .end packed-switch
.end method
