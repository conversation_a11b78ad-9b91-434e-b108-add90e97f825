
@echo off

del "%~dp0\custom_spider.jar"
rd /s/q "%~dp0\Smali_classes"

java -jar "%~dp0\3rd\apktool_2.11.0.jar" d -f "%~dp0\..\app\build\outputs\apk\release\app-release-unsigned.apk" -o "%~dp0\Smali_classes"

rd /s/q "%~dp0\spider.jar\smali\com\github\catvod\spider"
rd /s/q "%~dp0\spider.jar\smali\com\github\catvod\js"
rd /s/q "%~dp0\spider.jar\smali\org\slf4j\"
rd /s/q "%~dp0\spider.jar\lib\"

if not exist "%~dp0\spider.jar\smali\com\github\catvod\" md "%~dp0\spider.jar\smali\com\github\catvod\"
if not exist "%~dp0\spider.jar\smali\org\slf4j\" md "%~dp0\spider.jar\smali\org\slf4j\"
if not exist "%~dp0\spider.jar\assets\" md "%~dp0\spider.jar\assets\"
if not exist "%~dp0\spider.jar\lib\" md "%~dp0\spider.jar\lib\"

move "%~dp0\Smali_classes\smali\com\github\catvod\spider" "%~dp0\spider.jar\smali\com\github\catvod\"
move "%~dp0\Smali_classes\smali\com\github\catvod\js" "%~dp0\spider.jar\smali\com\github\catvod\"
move "%~dp0\Smali_classes\smali\org\slf4j" "%~dp0\spider.jar\smali\org\slf4j\"

echo 正在复制.so文件到jar...
if exist "%~dp0\Smali_classes\lib\" (
    echo 找到lib目录，开始复制.so文件
    xcopy "%~dp0\Smali_classes\lib\*" "%~dp0\spider.jar\lib\" /E /I /Y
) else (
    echo 警告：未找到lib目录，跳过.so文件复制
)

java -jar "%~dp0\3rd\apktool_2.11.0.jar" b "%~dp0\spider.jar" -c

if exist "%~dp0\spider.jar\dist\spider.jar" (
    move "%~dp0\spider.jar\dist\spider.jar" "%~dp0\custom_spider.jar"
    echo 成功生成custom_spider.jar
) else if exist "%~dp0\spider.jar\dist\dex.jar" (
    move "%~dp0\spider.jar\dist\dex.jar" "%~dp0\custom_spider.jar"
    echo 成功生成custom_spider.jar
) else (
    echo 错误：未找到生成的jar文件
    dir "%~dp0\spider.jar\dist\" /b
)

certUtil -hashfile "%~dp0\custom_spider.jar" MD5 | find /i /v "md5" | find /i /v "certutil" > "%~dp0\custom_spider.jar.md5"

rd /s/q "%~dp0\spider.jar\build"
rd /s/q "%~dp0\spider.jar\smali"
rd /s/q "%~dp0\spider.jar\dist"
rd /s/q "%~dp0\Smali_classes"
