.class public final Lcom/github/catvod/spider/merge/h/u;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/github/catvod/spider/merge/e/A;


# instance fields
.field public final a:Lcom/github/catvod/spider/merge/g/h;

.field public final b:Lcom/github/catvod/spider/merge/e/h;

.field public final c:Lcom/github/catvod/spider/merge/g/j;

.field public final d:Lcom/github/catvod/spider/merge/h/j;

.field public final e:Ljava/util/List;


# direct methods
.method public constructor <init>(Lcom/github/catvod/spider/merge/g/h;Lcom/github/catvod/spider/merge/e/h;Lcom/github/catvod/spider/merge/g/j;Lcom/github/catvod/spider/merge/h/j;Ljava/util/List;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lcom/github/catvod/spider/merge/h/u;->a:Lcom/github/catvod/spider/merge/g/h;

    .line 5
    .line 6
    iput-object p2, p0, Lcom/github/catvod/spider/merge/h/u;->b:Lcom/github/catvod/spider/merge/e/h;

    .line 7
    .line 8
    iput-object p3, p0, Lcom/github/catvod/spider/merge/h/u;->c:Lcom/github/catvod/spider/merge/g/j;

    .line 9
    .line 10
    iput-object p4, p0, Lcom/github/catvod/spider/merge/h/u;->d:Lcom/github/catvod/spider/merge/h/j;

    .line 11
    .line 12
    iput-object p5, p0, Lcom/github/catvod/spider/merge/h/u;->e:Ljava/util/List;

    .line 13
    .line 14
    return-void
.end method

.method public static b(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/reflect/Field;Ljava/lang/reflect/Field;)V
    .locals 3

    .line 1
    new-instance v0, Ljava/lang/IllegalArgumentException;

    .line 2
    .line 3
    new-instance v1, Ljava/lang/StringBuilder;

    .line 4
    .line 5
    const-string v2, "Class "

    .line 6
    .line 7
    invoke-direct {v1, v2}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 8
    .line 9
    .line 10
    invoke-virtual {p0}, Ljava/lang/Class;->getName()Ljava/lang/String;

    .line 11
    .line 12
    .line 13
    move-result-object p0

    .line 14
    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 15
    .line 16
    .line 17
    const-string p0, " declares multiple JSON fields named \'"

    .line 18
    .line 19
    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 20
    .line 21
    .line 22
    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 23
    .line 24
    .line 25
    const-string p0, "\'; conflict is caused by fields "

    .line 26
    .line 27
    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 28
    .line 29
    .line 30
    invoke-static {p2}, Lcom/github/catvod/spider/merge/i/c;->c(Ljava/lang/reflect/Field;)Ljava/lang/String;

    .line 31
    .line 32
    .line 33
    move-result-object p0

    .line 34
    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 35
    .line 36
    .line 37
    const-string p0, " and "

    .line 38
    .line 39
    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 40
    .line 41
    .line 42
    invoke-static {p3}, Lcom/github/catvod/spider/merge/i/c;->c(Ljava/lang/reflect/Field;)Ljava/lang/String;

    .line 43
    .line 44
    .line 45
    move-result-object p0

    .line 46
    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 47
    .line 48
    .line 49
    const-string p0, "\nSee "

    .line 50
    .line 51
    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 52
    .line 53
    .line 54
    const-string p0, "https://github.com/google/gson/blob/main/Troubleshooting.md#"

    .line 55
    .line 56
    const-string p1, "duplicate-fields"

    .line 57
    .line 58
    invoke-virtual {p0, p1}, Ljava/lang/String;->concat(Ljava/lang/String;)Ljava/lang/String;

    .line 59
    .line 60
    .line 61
    move-result-object p0

    .line 62
    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 63
    .line 64
    .line 65
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 66
    .line 67
    .line 68
    move-result-object p0

    .line 69
    invoke-direct {v0, p0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 70
    .line 71
    .line 72
    throw v0
.end method


# virtual methods
.method public final a(Lcom/github/catvod/spider/merge/e/n;Lcom/github/catvod/spider/merge/k/a;)Lcom/github/catvod/spider/merge/e/z;
    .locals 3

    .line 1
    const/4 v0, 0x1

    .line 2
    const-class v1, Ljava/lang/Object;

    .line 3
    .line 4
    iget-object v2, p2, Lcom/github/catvod/spider/merge/k/a;->a:Ljava/lang/Class;

    .line 5
    .line 6
    invoke-virtual {v1, v2}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    .line 7
    .line 8
    .line 9
    move-result v1

    .line 10
    if-nez v1, :cond_0

    .line 11
    .line 12
    const/4 p1, 0x0

    .line 13
    return-object p1

    .line 14
    :cond_0
    sget-object v1, Lcom/github/catvod/spider/merge/i/c;->a:Lcom/github/catvod/spider/merge/A/a;

    .line 15
    .line 16
    invoke-virtual {v2}, Ljava/lang/Class;->getModifiers()I

    .line 17
    .line 18
    .line 19
    move-result v1

    .line 20
    invoke-static {v1}, Ljava/lang/reflect/Modifier;->isStatic(I)Z

    .line 21
    .line 22
    .line 23
    move-result v1

    .line 24
    if-nez v1, :cond_2

    .line 25
    .line 26
    invoke-virtual {v2}, Ljava/lang/Class;->isAnonymousClass()Z

    .line 27
    .line 28
    .line 29
    move-result v1

    .line 30
    if-nez v1, :cond_1

    .line 31
    .line 32
    invoke-virtual {v2}, Ljava/lang/Class;->isLocalClass()Z

    .line 33
    .line 34
    .line 35
    move-result v1

    .line 36
    if-eqz v1, :cond_2

    .line 37
    .line 38
    :cond_1
    new-instance p1, Lcom/github/catvod/spider/merge/h/m;

    .line 39
    .line 40
    invoke-direct {p1, v0}, Lcom/github/catvod/spider/merge/h/m;-><init>(I)V

    .line 41
    .line 42
    .line 43
    return-object p1

    .line 44
    :cond_2
    iget-object v1, p0, Lcom/github/catvod/spider/merge/h/u;->e:Ljava/util/List;

    .line 45
    .line 46
    invoke-static {v1}, Lcom/github/catvod/spider/merge/g/d;->e(Ljava/util/List;)V

    .line 47
    .line 48
    .line 49
    sget-object v1, Lcom/github/catvod/spider/merge/i/c;->a:Lcom/github/catvod/spider/merge/A/a;

    .line 50
    .line 51
    invoke-virtual {v1, v2}, Lcom/github/catvod/spider/merge/A/a;->p(Ljava/lang/Class;)Z

    .line 52
    .line 53
    .line 54
    move-result v1

    .line 55
    if-eqz v1, :cond_3

    .line 56
    .line 57
    new-instance v1, Lcom/github/catvod/spider/merge/h/t;

    .line 58
    .line 59
    invoke-virtual {p0, p1, p2, v2, v0}, Lcom/github/catvod/spider/merge/h/u;->c(Lcom/github/catvod/spider/merge/e/n;Lcom/github/catvod/spider/merge/k/a;Ljava/lang/Class;Z)Lcom/github/catvod/spider/merge/h/s;

    .line 60
    .line 61
    .line 62
    move-result-object p1

    .line 63
    invoke-direct {v1, v2, p1}, Lcom/github/catvod/spider/merge/h/t;-><init>(Ljava/lang/Class;Lcom/github/catvod/spider/merge/h/s;)V

    .line 64
    .line 65
    .line 66
    return-object v1

    .line 67
    :cond_3
    iget-object v0, p0, Lcom/github/catvod/spider/merge/h/u;->a:Lcom/github/catvod/spider/merge/g/h;

    .line 68
    .line 69
    invoke-virtual {v0, p2}, Lcom/github/catvod/spider/merge/g/h;->b(Lcom/github/catvod/spider/merge/k/a;)Lcom/github/catvod/spider/merge/g/r;

    .line 70
    .line 71
    .line 72
    new-instance v0, Lcom/github/catvod/spider/merge/h/r;

    .line 73
    .line 74
    const/4 v1, 0x0

    .line 75
    invoke-virtual {p0, p1, p2, v2, v1}, Lcom/github/catvod/spider/merge/h/u;->c(Lcom/github/catvod/spider/merge/e/n;Lcom/github/catvod/spider/merge/k/a;Ljava/lang/Class;Z)Lcom/github/catvod/spider/merge/h/s;

    .line 76
    .line 77
    .line 78
    move-result-object p1

    .line 79
    invoke-direct {v0, p1}, Lcom/github/catvod/spider/merge/h/q;-><init>(Lcom/github/catvod/spider/merge/h/s;)V

    .line 80
    .line 81
    .line 82
    return-object v0
.end method

.method public final c(Lcom/github/catvod/spider/merge/e/n;Lcom/github/catvod/spider/merge/k/a;Ljava/lang/Class;Z)Lcom/github/catvod/spider/merge/h/s;
    .locals 22

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v7, p3

    .line 4
    .line 5
    invoke-virtual {v7}, Ljava/lang/Class;->isInterface()Z

    .line 6
    .line 7
    .line 8
    move-result v1

    .line 9
    if-eqz v1, :cond_0

    .line 10
    .line 11
    sget-object v1, Lcom/github/catvod/spider/merge/h/s;->b:Lcom/github/catvod/spider/merge/h/s;

    .line 12
    .line 13
    return-object v1

    .line 14
    :cond_0
    new-instance v8, Ljava/util/LinkedHashMap;

    .line 15
    .line 16
    invoke-direct {v8}, Ljava/util/LinkedHashMap;-><init>()V

    .line 17
    .line 18
    .line 19
    new-instance v9, Ljava/util/LinkedHashMap;

    .line 20
    .line 21
    invoke-direct {v9}, Ljava/util/LinkedHashMap;-><init>()V

    .line 22
    .line 23
    .line 24
    move-object/from16 v10, p2

    .line 25
    .line 26
    move-object v11, v7

    .line 27
    :goto_0
    const-class v1, Ljava/lang/Object;

    .line 28
    .line 29
    if-eq v11, v1, :cond_16

    .line 30
    .line 31
    invoke-virtual {v11}, Ljava/lang/Class;->getDeclaredFields()[Ljava/lang/reflect/Field;

    .line 32
    .line 33
    .line 34
    move-result-object v12

    .line 35
    if-eq v11, v7, :cond_1

    .line 36
    .line 37
    array-length v1, v12

    .line 38
    if-lez v1, :cond_1

    .line 39
    .line 40
    iget-object v1, v0, Lcom/github/catvod/spider/merge/h/u;->e:Ljava/util/List;

    .line 41
    .line 42
    invoke-static {v1}, Lcom/github/catvod/spider/merge/g/d;->e(Ljava/util/List;)V

    .line 43
    .line 44
    .line 45
    :cond_1
    array-length v13, v12

    .line 46
    const/4 v14, 0x0

    .line 47
    const/4 v15, 0x0

    .line 48
    :goto_1
    iget-object v1, v10, Lcom/github/catvod/spider/merge/k/a;->b:Ljava/lang/reflect/Type;

    .line 49
    .line 50
    if-ge v15, v13, :cond_15

    .line 51
    .line 52
    aget-object v2, v12, v15

    .line 53
    .line 54
    const/4 v3, 0x1

    .line 55
    invoke-virtual {v0, v2, v3}, Lcom/github/catvod/spider/merge/h/u;->d(Ljava/lang/reflect/Field;Z)Z

    .line 56
    .line 57
    .line 58
    move-result v16

    .line 59
    invoke-virtual {v0, v2, v14}, Lcom/github/catvod/spider/merge/h/u;->d(Ljava/lang/reflect/Field;Z)Z

    .line 60
    .line 61
    .line 62
    move-result v4

    .line 63
    if-nez v16, :cond_2

    .line 64
    .line 65
    if-nez v4, :cond_2

    .line 66
    .line 67
    move-object/from16 v3, p1

    .line 68
    .line 69
    move-object/from16 v21, v10

    .line 70
    .line 71
    move-object/from16 p2, v12

    .line 72
    .line 73
    const/16 v20, 0x0

    .line 74
    .line 75
    goto/16 :goto_b

    .line 76
    .line 77
    :cond_2
    const/16 v17, 0x0

    .line 78
    .line 79
    const-class v5, Lcom/github/catvod/spider/merge/f/b;

    .line 80
    .line 81
    if-eqz p4, :cond_6

    .line 82
    .line 83
    invoke-virtual {v2}, Ljava/lang/reflect/Field;->getModifiers()I

    .line 84
    .line 85
    .line 86
    move-result v6

    .line 87
    invoke-static {v6}, Ljava/lang/reflect/Modifier;->isStatic(I)Z

    .line 88
    .line 89
    .line 90
    move-result v6

    .line 91
    if-eqz v6, :cond_3

    .line 92
    .line 93
    move-object/from16 v6, v17

    .line 94
    .line 95
    const/16 v18, 0x0

    .line 96
    .line 97
    goto :goto_3

    .line 98
    :cond_3
    sget-object v6, Lcom/github/catvod/spider/merge/i/c;->a:Lcom/github/catvod/spider/merge/A/a;

    .line 99
    .line 100
    invoke-virtual {v6, v11, v2}, Lcom/github/catvod/spider/merge/A/a;->k(Ljava/lang/Class;Ljava/lang/reflect/Field;)Ljava/lang/reflect/Method;

    .line 101
    .line 102
    .line 103
    move-result-object v6

    .line 104
    invoke-static {v6}, Lcom/github/catvod/spider/merge/i/c;->f(Ljava/lang/reflect/AccessibleObject;)V

    .line 105
    .line 106
    .line 107
    invoke-virtual {v6, v5}, Ljava/lang/reflect/Method;->getAnnotation(Ljava/lang/Class;)Ljava/lang/annotation/Annotation;

    .line 108
    .line 109
    .line 110
    move-result-object v18

    .line 111
    if-eqz v18, :cond_5

    .line 112
    .line 113
    invoke-virtual {v2, v5}, Ljava/lang/reflect/Field;->getAnnotation(Ljava/lang/Class;)Ljava/lang/annotation/Annotation;

    .line 114
    .line 115
    .line 116
    move-result-object v18

    .line 117
    if-eqz v18, :cond_4

    .line 118
    .line 119
    goto :goto_2

    .line 120
    :cond_4
    invoke-static {v6, v14}, Lcom/github/catvod/spider/merge/i/c;->d(Ljava/lang/reflect/AccessibleObject;Z)Ljava/lang/String;

    .line 121
    .line 122
    .line 123
    move-result-object v1

    .line 124
    new-instance v2, Lcom/github/catvod/spider/merge/e/q;

    .line 125
    .line 126
    new-instance v3, Ljava/lang/StringBuilder;

    .line 127
    .line 128
    const-string v4, "@SerializedName on "

    .line 129
    .line 130
    invoke-direct {v3, v4}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 131
    .line 132
    .line 133
    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 134
    .line 135
    .line 136
    const-string v1, " is not supported"

    .line 137
    .line 138
    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 139
    .line 140
    .line 141
    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 142
    .line 143
    .line 144
    move-result-object v1

    .line 145
    invoke-direct {v2, v1}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;)V

    .line 146
    .line 147
    .line 148
    throw v2

    .line 149
    :cond_5
    :goto_2
    move/from16 v18, v4

    .line 150
    .line 151
    goto :goto_3

    .line 152
    :cond_6
    move/from16 v18, v4

    .line 153
    .line 154
    move-object/from16 v6, v17

    .line 155
    .line 156
    :goto_3
    if-nez v6, :cond_7

    .line 157
    .line 158
    invoke-static {v2}, Lcom/github/catvod/spider/merge/i/c;->f(Ljava/lang/reflect/AccessibleObject;)V

    .line 159
    .line 160
    .line 161
    :cond_7
    invoke-virtual {v2}, Ljava/lang/reflect/Field;->getGenericType()Ljava/lang/reflect/Type;

    .line 162
    .line 163
    .line 164
    move-result-object v4

    .line 165
    const/16 p2, 0x1

    .line 166
    .line 167
    new-instance v3, Ljava/util/HashMap;

    .line 168
    .line 169
    invoke-direct {v3}, Ljava/util/HashMap;-><init>()V

    .line 170
    .line 171
    .line 172
    invoke-static {v1, v11, v4, v3}, Lcom/github/catvod/spider/merge/g/d;->h(Ljava/lang/reflect/Type;Ljava/lang/Class;Ljava/lang/reflect/Type;Ljava/util/HashMap;)Ljava/lang/reflect/Type;

    .line 173
    .line 174
    .line 175
    move-result-object v1

    .line 176
    invoke-virtual {v2, v5}, Ljava/lang/reflect/Field;->getAnnotation(Ljava/lang/Class;)Ljava/lang/annotation/Annotation;

    .line 177
    .line 178
    .line 179
    move-result-object v3

    .line 180
    check-cast v3, Lcom/github/catvod/spider/merge/f/b;

    .line 181
    .line 182
    if-nez v3, :cond_8

    .line 183
    .line 184
    iget-object v3, v0, Lcom/github/catvod/spider/merge/h/u;->b:Lcom/github/catvod/spider/merge/e/h;

    .line 185
    .line 186
    invoke-virtual {v3, v2}, Lcom/github/catvod/spider/merge/e/h;->b(Ljava/lang/reflect/Field;)Ljava/lang/String;

    .line 187
    .line 188
    .line 189
    move-result-object v3

    .line 190
    invoke-static {v3}, Ljava/util/Collections;->singletonList(Ljava/lang/Object;)Ljava/util/List;

    .line 191
    .line 192
    .line 193
    move-result-object v3

    .line 194
    :goto_4
    move-object v14, v3

    .line 195
    :goto_5
    const/4 v3, 0x0

    .line 196
    goto :goto_6

    .line 197
    :cond_8
    invoke-interface {v3}, Lcom/github/catvod/spider/merge/f/b;->value()Ljava/lang/String;

    .line 198
    .line 199
    .line 200
    move-result-object v4

    .line 201
    invoke-interface {v3}, Lcom/github/catvod/spider/merge/f/b;->alternate()[Ljava/lang/String;

    .line 202
    .line 203
    .line 204
    move-result-object v3

    .line 205
    array-length v5, v3

    .line 206
    if-nez v5, :cond_9

    .line 207
    .line 208
    invoke-static {v4}, Ljava/util/Collections;->singletonList(Ljava/lang/Object;)Ljava/util/List;

    .line 209
    .line 210
    .line 211
    move-result-object v3

    .line 212
    goto :goto_4

    .line 213
    :cond_9
    new-instance v5, Ljava/util/ArrayList;

    .line 214
    .line 215
    array-length v14, v3

    .line 216
    add-int/lit8 v14, v14, 0x1

    .line 217
    .line 218
    invoke-direct {v5, v14}, Ljava/util/ArrayList;-><init>(I)V

    .line 219
    .line 220
    .line 221
    invoke-virtual {v5, v4}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 222
    .line 223
    .line 224
    invoke-static {v5, v3}, Ljava/util/Collections;->addAll(Ljava/util/Collection;[Ljava/lang/Object;)Z

    .line 225
    .line 226
    .line 227
    move-object v14, v5

    .line 228
    goto :goto_5

    .line 229
    :goto_6
    invoke-interface {v14, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 230
    .line 231
    .line 232
    move-result-object v4

    .line 233
    check-cast v4, Ljava/lang/String;

    .line 234
    .line 235
    move-object v5, v4

    .line 236
    new-instance v4, Lcom/github/catvod/spider/merge/k/a;

    .line 237
    .line 238
    invoke-direct {v4, v1}, Lcom/github/catvod/spider/merge/k/a;-><init>(Ljava/lang/reflect/Type;)V

    .line 239
    .line 240
    .line 241
    iget-object v1, v4, Lcom/github/catvod/spider/merge/k/a;->a:Ljava/lang/Class;

    .line 242
    .line 243
    instance-of v3, v1, Ljava/lang/Class;

    .line 244
    .line 245
    if-eqz v3, :cond_a

    .line 246
    .line 247
    invoke-virtual {v1}, Ljava/lang/Class;->isPrimitive()Z

    .line 248
    .line 249
    .line 250
    move-result v1

    .line 251
    :cond_a
    invoke-virtual {v2}, Ljava/lang/reflect/Field;->getModifiers()I

    .line 252
    .line 253
    .line 254
    move-result v1

    .line 255
    invoke-static {v1}, Ljava/lang/reflect/Modifier;->isStatic(I)Z

    .line 256
    .line 257
    .line 258
    move-result v3

    .line 259
    if-eqz v3, :cond_b

    .line 260
    .line 261
    invoke-static {v1}, Ljava/lang/reflect/Modifier;->isFinal(I)Z

    .line 262
    .line 263
    .line 264
    move-result v1

    .line 265
    :cond_b
    const-class v1, Lcom/github/catvod/spider/merge/f/a;

    .line 266
    .line 267
    invoke-virtual {v2, v1}, Ljava/lang/reflect/Field;->getAnnotation(Ljava/lang/Class;)Ljava/lang/annotation/Annotation;

    .line 268
    .line 269
    .line 270
    move-result-object v1

    .line 271
    check-cast v1, Lcom/github/catvod/spider/merge/f/a;

    .line 272
    .line 273
    if-eqz v1, :cond_c

    .line 274
    .line 275
    move-object v3, v5

    .line 276
    move-object v5, v1

    .line 277
    iget-object v1, v0, Lcom/github/catvod/spider/merge/h/u;->d:Lcom/github/catvod/spider/merge/h/j;

    .line 278
    .line 279
    move-object/from16 v20, v2

    .line 280
    .line 281
    iget-object v2, v0, Lcom/github/catvod/spider/merge/h/u;->a:Lcom/github/catvod/spider/merge/g/h;

    .line 282
    .line 283
    move-object/from16 v21, v6

    .line 284
    .line 285
    const/4 v6, 0x0

    .line 286
    move-object/from16 p2, v21

    .line 287
    .line 288
    move-object/from16 v21, v10

    .line 289
    .line 290
    move-object/from16 v10, p2

    .line 291
    .line 292
    move-object/from16 p2, v12

    .line 293
    .line 294
    move-object/from16 v0, v20

    .line 295
    .line 296
    const/16 v19, 0x1

    .line 297
    .line 298
    const/16 v20, 0x0

    .line 299
    .line 300
    move-object v12, v3

    .line 301
    move-object/from16 v3, p1

    .line 302
    .line 303
    invoke-virtual/range {v1 .. v6}, Lcom/github/catvod/spider/merge/h/j;->b(Lcom/github/catvod/spider/merge/g/h;Lcom/github/catvod/spider/merge/e/n;Lcom/github/catvod/spider/merge/k/a;Lcom/github/catvod/spider/merge/f/a;Z)Lcom/github/catvod/spider/merge/e/z;

    .line 304
    .line 305
    .line 306
    move-result-object v1

    .line 307
    goto :goto_7

    .line 308
    :cond_c
    move-object/from16 v3, p1

    .line 309
    .line 310
    move-object v0, v2

    .line 311
    move-object/from16 v21, v10

    .line 312
    .line 313
    move-object/from16 p2, v12

    .line 314
    .line 315
    const/16 v19, 0x1

    .line 316
    .line 317
    const/16 v20, 0x0

    .line 318
    .line 319
    move-object v12, v5

    .line 320
    move-object v10, v6

    .line 321
    move-object/from16 v1, v17

    .line 322
    .line 323
    :goto_7
    if-eqz v1, :cond_d

    .line 324
    .line 325
    goto :goto_8

    .line 326
    :cond_d
    const/16 v19, 0x0

    .line 327
    .line 328
    :goto_8
    if-nez v1, :cond_e

    .line 329
    .line 330
    invoke-virtual {v3, v4}, Lcom/github/catvod/spider/merge/e/n;->b(Lcom/github/catvod/spider/merge/k/a;)Lcom/github/catvod/spider/merge/e/z;

    .line 331
    .line 332
    .line 333
    move-result-object v1

    .line 334
    :cond_e
    if-eqz v16, :cond_10

    .line 335
    .line 336
    if-eqz v19, :cond_f

    .line 337
    .line 338
    goto :goto_9

    .line 339
    :cond_f
    new-instance v2, Lcom/github/catvod/spider/merge/h/w;

    .line 340
    .line 341
    iget-object v4, v4, Lcom/github/catvod/spider/merge/k/a;->b:Ljava/lang/reflect/Type;

    .line 342
    .line 343
    invoke-direct {v2, v3, v1, v4}, Lcom/github/catvod/spider/merge/h/w;-><init>(Lcom/github/catvod/spider/merge/e/n;Lcom/github/catvod/spider/merge/e/z;Ljava/lang/reflect/Type;)V

    .line 344
    .line 345
    .line 346
    move-object v1, v2

    .line 347
    :cond_10
    :goto_9
    new-instance v2, Lcom/github/catvod/spider/merge/h/p;

    .line 348
    .line 349
    invoke-direct {v2, v12, v0, v10, v1}, Lcom/github/catvod/spider/merge/h/p;-><init>(Ljava/lang/String;Ljava/lang/reflect/Field;Ljava/lang/reflect/Method;Lcom/github/catvod/spider/merge/e/z;)V

    .line 350
    .line 351
    .line 352
    if-eqz v18, :cond_12

    .line 353
    .line 354
    invoke-interface {v14}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    .line 355
    .line 356
    .line 357
    move-result-object v1

    .line 358
    :goto_a
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 359
    .line 360
    .line 361
    move-result v4

    .line 362
    if-eqz v4, :cond_12

    .line 363
    .line 364
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 365
    .line 366
    .line 367
    move-result-object v4

    .line 368
    check-cast v4, Ljava/lang/String;

    .line 369
    .line 370
    invoke-interface {v8, v4, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 371
    .line 372
    .line 373
    move-result-object v5

    .line 374
    check-cast v5, Lcom/github/catvod/spider/merge/h/p;

    .line 375
    .line 376
    if-nez v5, :cond_11

    .line 377
    .line 378
    goto :goto_a

    .line 379
    :cond_11
    iget-object v1, v5, Lcom/github/catvod/spider/merge/h/p;->b:Ljava/lang/reflect/Field;

    .line 380
    .line 381
    invoke-static {v7, v4, v1, v0}, Lcom/github/catvod/spider/merge/h/u;->b(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/reflect/Field;Ljava/lang/reflect/Field;)V

    .line 382
    .line 383
    .line 384
    throw v17

    .line 385
    :cond_12
    if-eqz v16, :cond_14

    .line 386
    .line 387
    invoke-interface {v9, v12, v2}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 388
    .line 389
    .line 390
    move-result-object v1

    .line 391
    check-cast v1, Lcom/github/catvod/spider/merge/h/p;

    .line 392
    .line 393
    if-nez v1, :cond_13

    .line 394
    .line 395
    goto :goto_b

    .line 396
    :cond_13
    iget-object v1, v1, Lcom/github/catvod/spider/merge/h/p;->b:Ljava/lang/reflect/Field;

    .line 397
    .line 398
    invoke-static {v7, v12, v1, v0}, Lcom/github/catvod/spider/merge/h/u;->b(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/reflect/Field;Ljava/lang/reflect/Field;)V

    .line 399
    .line 400
    .line 401
    throw v17

    .line 402
    :cond_14
    :goto_b
    add-int/lit8 v15, v15, 0x1

    .line 403
    .line 404
    const/4 v14, 0x0

    .line 405
    move-object/from16 v0, p0

    .line 406
    .line 407
    move-object/from16 v12, p2

    .line 408
    .line 409
    move-object/from16 v10, v21

    .line 410
    .line 411
    goto/16 :goto_1

    .line 412
    .line 413
    :cond_15
    move-object/from16 v3, p1

    .line 414
    .line 415
    invoke-virtual {v11}, Ljava/lang/Class;->getGenericSuperclass()Ljava/lang/reflect/Type;

    .line 416
    .line 417
    .line 418
    move-result-object v0

    .line 419
    new-instance v2, Ljava/util/HashMap;

    .line 420
    .line 421
    invoke-direct {v2}, Ljava/util/HashMap;-><init>()V

    .line 422
    .line 423
    .line 424
    invoke-static {v1, v11, v0, v2}, Lcom/github/catvod/spider/merge/g/d;->h(Ljava/lang/reflect/Type;Ljava/lang/Class;Ljava/lang/reflect/Type;Ljava/util/HashMap;)Ljava/lang/reflect/Type;

    .line 425
    .line 426
    .line 427
    move-result-object v0

    .line 428
    new-instance v10, Lcom/github/catvod/spider/merge/k/a;

    .line 429
    .line 430
    invoke-direct {v10, v0}, Lcom/github/catvod/spider/merge/k/a;-><init>(Ljava/lang/reflect/Type;)V

    .line 431
    .line 432
    .line 433
    iget-object v11, v10, Lcom/github/catvod/spider/merge/k/a;->a:Ljava/lang/Class;

    .line 434
    .line 435
    move-object/from16 v0, p0

    .line 436
    .line 437
    goto/16 :goto_0

    .line 438
    .line 439
    :cond_16
    new-instance v0, Lcom/github/catvod/spider/merge/h/s;

    .line 440
    .line 441
    new-instance v1, Ljava/util/ArrayList;

    .line 442
    .line 443
    invoke-virtual {v9}, Ljava/util/LinkedHashMap;->values()Ljava/util/Collection;

    .line 444
    .line 445
    .line 446
    move-result-object v2

    .line 447
    invoke-direct {v1, v2}, Ljava/util/ArrayList;-><init>(Ljava/util/Collection;)V

    .line 448
    .line 449
    .line 450
    invoke-direct {v0, v1}, Lcom/github/catvod/spider/merge/h/s;-><init>(Ljava/util/List;)V

    .line 451
    .line 452
    .line 453
    return-object v0
.end method

.method public final d(Ljava/lang/reflect/Field;Z)Z
    .locals 3

    .line 1
    iget-object v0, p0, Lcom/github/catvod/spider/merge/h/u;->c:Lcom/github/catvod/spider/merge/g/j;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 4
    .line 5
    .line 6
    invoke-virtual {p1}, Ljava/lang/reflect/Field;->getModifiers()I

    .line 7
    .line 8
    .line 9
    move-result v1

    .line 10
    const/16 v2, 0x88

    .line 11
    .line 12
    and-int/2addr v1, v2

    .line 13
    const/4 v2, 0x1

    .line 14
    if-eqz v1, :cond_0

    .line 15
    .line 16
    :goto_0
    const/4 p1, 0x1

    .line 17
    goto :goto_3

    .line 18
    :cond_0
    invoke-virtual {p1}, Ljava/lang/reflect/Field;->isSynthetic()Z

    .line 19
    .line 20
    .line 21
    move-result v1

    .line 22
    if-eqz v1, :cond_1

    .line 23
    .line 24
    goto :goto_0

    .line 25
    :cond_1
    invoke-virtual {p1}, Ljava/lang/reflect/Field;->getType()Ljava/lang/Class;

    .line 26
    .line 27
    .line 28
    move-result-object p1

    .line 29
    invoke-virtual {v0, p1, p2}, Lcom/github/catvod/spider/merge/g/j;->b(Ljava/lang/Class;Z)Z

    .line 30
    .line 31
    .line 32
    move-result p1

    .line 33
    if-eqz p1, :cond_2

    .line 34
    .line 35
    goto :goto_0

    .line 36
    :cond_2
    if-eqz p2, :cond_3

    .line 37
    .line 38
    iget-object p1, v0, Lcom/github/catvod/spider/merge/g/j;->a:Ljava/util/List;

    .line 39
    .line 40
    goto :goto_1

    .line 41
    :cond_3
    iget-object p1, v0, Lcom/github/catvod/spider/merge/g/j;->b:Ljava/util/List;

    .line 42
    .line 43
    :goto_1
    invoke-interface {p1}, Ljava/util/List;->isEmpty()Z

    .line 44
    .line 45
    .line 46
    move-result p2

    .line 47
    if-nez p2, :cond_5

    .line 48
    .line 49
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    .line 50
    .line 51
    .line 52
    move-result-object p1

    .line 53
    invoke-interface {p1}, Ljava/util/Iterator;->hasNext()Z

    .line 54
    .line 55
    .line 56
    move-result p2

    .line 57
    if-nez p2, :cond_4

    .line 58
    .line 59
    goto :goto_2

    .line 60
    :cond_4
    invoke-interface {p1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 65
    .line 66
    .line 67
    new-instance p1, Ljava/lang/ClassCastException;

    .line 68
    .line 69
    invoke-direct {p1}, Ljava/lang/ClassCastException;-><init>()V

    .line 70
    .line 71
    .line 72
    throw p1

    .line 73
    :cond_5
    :goto_2
    const/4 p1, 0x0

    .line 74
    :goto_3
    xor-int/2addr p1, v2

    .line 75
    return p1
.end method
