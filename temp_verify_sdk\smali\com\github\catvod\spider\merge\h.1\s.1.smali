.class public final Lcom/github/catvod/spider/merge/h/s;
.super Ljava/lang/Object;
.source "SourceFile"


# static fields
.field public static final b:Lcom/github/catvod/spider/merge/h/s;


# instance fields
.field public final a:Ljava/util/List;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, Lcom/github/catvod/spider/merge/h/s;

    .line 2
    .line 3
    sget-object v1, Ljava/util/Collections;->EMPTY_MAP:Ljava/util/Map;

    .line 4
    .line 5
    sget-object v1, Ljava/util/Collections;->EMPTY_LIST:Ljava/util/List;

    .line 6
    .line 7
    invoke-direct {v0, v1}, Lcom/github/catvod/spider/merge/h/s;-><init>(Ljava/util/List;)V

    .line 8
    .line 9
    .line 10
    sput-object v0, Lcom/github/catvod/spider/merge/h/s;->b:Lcom/github/catvod/spider/merge/h/s;

    .line 11
    .line 12
    return-void
.end method

.method public constructor <init>(<PERSON><PERSON><PERSON>/util/List;)V
    .locals 0

    .line 1
    invoke-direct {p0}, <PERSON><PERSON><PERSON>/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lcom/github/catvod/spider/merge/h/s;->a:Ljava/util/List;

    .line 5
    .line 6
    return-void
.end method
