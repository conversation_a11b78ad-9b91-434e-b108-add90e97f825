.class public final Lcom/github/catvod/spider/merge/h/k;
.super Lcom/github/catvod/spider/merge/e/z;
.source "SourceFile"


# instance fields
.field public final a:Lcom/github/catvod/spider/merge/h/w;

.field public final synthetic b:Lcom/github/catvod/spider/merge/h/d;


# direct methods
.method public constructor <init>(Lcom/github/catvod/spider/merge/h/d;Lcom/github/catvod/spider/merge/e/n;Ljava/lang/reflect/Type;Lcom/github/catvod/spider/merge/e/z;Ljava/lang/reflect/Type;Lcom/github/catvod/spider/merge/e/z;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lcom/github/catvod/spider/merge/h/k;->b:Lcom/github/catvod/spider/merge/h/d;

    .line 5
    .line 6
    new-instance p1, Lcom/github/catvod/spider/merge/h/w;

    .line 7
    .line 8
    invoke-direct {p1, p2, p4, p3}, Lcom/github/catvod/spider/merge/h/w;-><init>(Lcom/github/catvod/spider/merge/e/n;Lcom/github/catvod/spider/merge/e/z;Ljava/lang/reflect/Type;)V

    .line 9
    .line 10
    .line 11
    new-instance p1, Lcom/github/catvod/spider/merge/h/w;

    .line 12
    .line 13
    invoke-direct {p1, p2, p6, p5}, Lcom/github/catvod/spider/merge/h/w;-><init>(Lcom/github/catvod/spider/merge/e/n;Lcom/github/catvod/spider/merge/e/z;Ljava/lang/reflect/Type;)V

    .line 14
    .line 15
    .line 16
    iput-object p1, p0, Lcom/github/catvod/spider/merge/h/k;->a:Lcom/github/catvod/spider/merge/h/w;

    .line 17
    .line 18
    return-void
.end method


# virtual methods
.method public final a(Lcom/github/catvod/spider/merge/l/a;Ljava/lang/Object;)V
    .locals 3

    .line 1
    check-cast p2, Ljava/util/Map;

    .line 2
    .line 3
    if-nez p2, :cond_0

    .line 4
    .line 5
    invoke-virtual {p1}, Lcom/github/catvod/spider/merge/l/a;->i()Lcom/github/catvod/spider/merge/l/a;

    .line 6
    .line 7
    .line 8
    return-void

    .line 9
    :cond_0
    iget-object v0, p0, Lcom/github/catvod/spider/merge/h/k;->b:Lcom/github/catvod/spider/merge/h/d;

    .line 10
    .line 11
    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 12
    .line 13
    .line 14
    iget-object v0, p0, Lcom/github/catvod/spider/merge/h/k;->a:Lcom/github/catvod/spider/merge/h/w;

    .line 15
    .line 16
    invoke-virtual {p1}, Lcom/github/catvod/spider/merge/l/a;->c()V

    .line 17
    .line 18
    .line 19
    invoke-interface {p2}, Ljava/util/Map;->entrySet()Ljava/util/Set;

    .line 20
    .line 21
    .line 22
    move-result-object p2

    .line 23
    invoke-interface {p2}, Ljava/util/Set;->iterator()Ljava/util/Iterator;

    .line 24
    .line 25
    .line 26
    move-result-object p2

    .line 27
    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 28
    .line 29
    .line 30
    move-result v1

    .line 31
    if-eqz v1, :cond_1

    .line 32
    .line 33
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 34
    .line 35
    .line 36
    move-result-object v1

    .line 37
    check-cast v1, Ljava/util/Map$Entry;

    .line 38
    .line 39
    invoke-interface {v1}, Ljava/util/Map$Entry;->getKey()Ljava/lang/Object;

    .line 40
    .line 41
    .line 42
    move-result-object v2

    .line 43
    invoke-static {v2}, Ljava/lang/String;->valueOf(Ljava/lang/Object;)Ljava/lang/String;

    .line 44
    .line 45
    .line 46
    move-result-object v2

    .line 47
    invoke-virtual {p1, v2}, Lcom/github/catvod/spider/merge/l/a;->g(Ljava/lang/String;)V

    .line 48
    .line 49
    .line 50
    invoke-interface {v1}, Ljava/util/Map$Entry;->getValue()Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    move-result-object v1

    .line 54
    invoke-virtual {v0, p1, v1}, Lcom/github/catvod/spider/merge/h/w;->a(Lcom/github/catvod/spider/merge/l/a;Ljava/lang/Object;)V

    .line 55
    .line 56
    .line 57
    goto :goto_0

    .line 58
    :cond_1
    invoke-virtual {p1}, Lcom/github/catvod/spider/merge/l/a;->f()V

    .line 59
    .line 60
    .line 61
    return-void
.end method
