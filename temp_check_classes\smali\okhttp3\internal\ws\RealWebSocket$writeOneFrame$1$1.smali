.class final Lokhttp3/internal/ws/RealWebSocket$writeOneFrame$1$1;
.super Lcom/github/catvod/spider/merge/C/g;
.source "SourceFile"

# interfaces
.implements Lcom/github/catvod/spider/merge/B/a;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/github/catvod/spider/merge/C/g;",
        "Lcom/github/catvod/spider/merge/B/a;"
    }
.end annotation


# instance fields
.field final synthetic this$0:Lokhttp3/internal/ws/RealWebSocket;


# direct methods
.method public constructor <init>(Lokhttp3/internal/ws/RealWebSocket;)V
    .locals 0

    iput-object p1, p0, Lokhttp3/internal/ws/RealWebSocket$writeOneFrame$1$1;->this$0:Lokhttp3/internal/ws/RealWebSocket;

    const/4 p1, 0x0

    invoke-direct {p0, p1}, Lcom/github/catvod/spider/merge/C/g;-><init>(I)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lokhttp3/internal/ws/RealWebSocket$writeOneFrame$1$1;->invoke()V

    sget-object v0, Lcom/github/catvod/spider/merge/p/f;->c:Lcom/github/catvod/spider/merge/p/f;

    return-object v0
.end method

.method public final invoke()V
    .locals 1

    .line 2
    iget-object v0, p0, Lokhttp3/internal/ws/RealWebSocket$writeOneFrame$1$1;->this$0:Lokhttp3/internal/ws/RealWebSocket;

    invoke-virtual {v0}, Lokhttp3/internal/ws/RealWebSocket;->cancel()V

    return-void
.end method
