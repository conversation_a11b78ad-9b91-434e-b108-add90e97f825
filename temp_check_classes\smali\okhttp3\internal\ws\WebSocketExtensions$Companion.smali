.class public final Lokhttp3/internal/ws/WebSocketExtensions$Companion;
.super Ljava/lang/Object;
.source "SourceFile"


# direct methods
.method private constructor <init>()V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lcom/github/catvod/spider/merge/C/d;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Lokhttp3/internal/ws/WebSocketExtensions$Companion;-><init>()V

    return-void
.end method


# virtual methods
.method public final parse(Lokhttp3/Headers;)Lokhttp3/internal/ws/WebSocketExtensions;
    .locals 18

    .line 1
    move-object/from16 v0, p1

    .line 2
    .line 3
    const-string v1, "responseHeaders"

    .line 4
    .line 5
    invoke-static {v0, v1}, Lcom/github/catvod/spider/merge/C/f;->e(<PERSON>java/lang/Object;Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    invoke-virtual {v0}, Lokhttp3/Headers;->size()I

    .line 9
    .line 10
    .line 11
    move-result v1

    .line 12
    const/4 v2, 0x0

    .line 13
    const/4 v4, 0x0

    .line 14
    const/4 v6, 0x0

    .line 15
    const/4 v7, 0x0

    .line 16
    const/4 v8, 0x0

    .line 17
    const/4 v9, 0x0

    .line 18
    const/4 v10, 0x0

    .line 19
    const/4 v11, 0x0

    .line 20
    :goto_0
    if-ge v4, v1, :cond_14

    .line 21
    .line 22
    invoke-virtual {v0, v4}, Lokhttp3/Headers;->name(I)Ljava/lang/String;

    .line 23
    .line 24
    .line 25
    move-result-object v5

    .line 26
    const-string v12, "Sec-WebSocket-Extensions"

    .line 27
    .line 28
    invoke-static {v5, v12}, Lcom/github/catvod/spider/merge/H/r;->y(Ljava/lang/String;Ljava/lang/String;)Z

    .line 29
    .line 30
    .line 31
    move-result v5

    .line 32
    if-nez v5, :cond_0

    .line 33
    .line 34
    goto/16 :goto_8

    .line 35
    .line 36
    :cond_0
    invoke-virtual {v0, v4}, Lokhttp3/Headers;->value(I)Ljava/lang/String;

    .line 37
    .line 38
    .line 39
    move-result-object v12

    .line 40
    const/4 v14, 0x0

    .line 41
    :goto_1
    invoke-virtual {v12}, Ljava/lang/String;->length()I

    .line 42
    .line 43
    .line 44
    move-result v5

    .line 45
    if-ge v14, v5, :cond_13

    .line 46
    .line 47
    const/16 v13, 0x2c

    .line 48
    .line 49
    const/4 v15, 0x0

    .line 50
    const/16 v16, 0x4

    .line 51
    .line 52
    const/16 v17, 0x0

    .line 53
    .line 54
    invoke-static/range {v12 .. v17}, Lokhttp3/internal/_UtilCommonKt;->delimiterOffset$default(Ljava/lang/String;CIIILjava/lang/Object;)I

    .line 55
    .line 56
    .line 57
    move-result v5

    .line 58
    const/16 v13, 0x3b

    .line 59
    .line 60
    invoke-static {v12, v13, v14, v5}, Lokhttp3/internal/_UtilCommonKt;->delimiterOffset(Ljava/lang/String;CII)I

    .line 61
    .line 62
    .line 63
    move-result v15

    .line 64
    invoke-static {v12, v14, v15}, Lokhttp3/internal/_UtilCommonKt;->trimSubstring(Ljava/lang/String;II)Ljava/lang/String;

    .line 65
    .line 66
    .line 67
    move-result-object v14

    .line 68
    const/4 v3, 0x1

    .line 69
    add-int/2addr v15, v3

    .line 70
    const/16 v17, 0x1

    .line 71
    .line 72
    const-string v3, "permessage-deflate"

    .line 73
    .line 74
    invoke-static {v14, v3}, Lcom/github/catvod/spider/merge/H/r;->y(Ljava/lang/String;Ljava/lang/String;)Z

    .line 75
    .line 76
    .line 77
    move-result v3

    .line 78
    if-eqz v3, :cond_12

    .line 79
    .line 80
    if-eqz v6, :cond_1

    .line 81
    .line 82
    const/4 v11, 0x1

    .line 83
    :cond_1
    move v14, v15

    .line 84
    :goto_2
    if-ge v14, v5, :cond_11

    .line 85
    .line 86
    invoke-static {v12, v13, v14, v5}, Lokhttp3/internal/_UtilCommonKt;->delimiterOffset(Ljava/lang/String;CII)I

    .line 87
    .line 88
    .line 89
    move-result v3

    .line 90
    const/16 v6, 0x3d

    .line 91
    .line 92
    invoke-static {v12, v6, v14, v3}, Lokhttp3/internal/_UtilCommonKt;->delimiterOffset(Ljava/lang/String;CII)I

    .line 93
    .line 94
    .line 95
    move-result v6

    .line 96
    invoke-static {v12, v14, v6}, Lokhttp3/internal/_UtilCommonKt;->trimSubstring(Ljava/lang/String;II)Ljava/lang/String;

    .line 97
    .line 98
    .line 99
    move-result-object v14

    .line 100
    if-ge v6, v3, :cond_3

    .line 101
    .line 102
    add-int/lit8 v6, v6, 0x1

    .line 103
    .line 104
    invoke-static {v12, v6, v3}, Lokhttp3/internal/_UtilCommonKt;->trimSubstring(Ljava/lang/String;II)Ljava/lang/String;

    .line 105
    .line 106
    .line 107
    move-result-object v6

    .line 108
    const-string v15, "<this>"

    .line 109
    .line 110
    invoke-static {v6, v15}, Lcom/github/catvod/spider/merge/C/f;->e(Ljava/lang/Object;Ljava/lang/String;)V

    .line 111
    .line 112
    .line 113
    invoke-virtual {v6}, Ljava/lang/String;->length()I

    .line 114
    .line 115
    .line 116
    move-result v15

    .line 117
    const/4 v13, 0x2

    .line 118
    if-lt v15, v13, :cond_2

    .line 119
    .line 120
    const-string v13, "\""

    .line 121
    .line 122
    invoke-static {v6, v13}, Lcom/github/catvod/spider/merge/H/j;->R(Ljava/lang/String;Ljava/lang/String;)Z

    .line 123
    .line 124
    .line 125
    move-result v15

    .line 126
    if-eqz v15, :cond_2

    .line 127
    .line 128
    invoke-static {v6, v13, v2}, Lcom/github/catvod/spider/merge/H/r;->x(Ljava/lang/String;Ljava/lang/String;Z)Z

    .line 129
    .line 130
    .line 131
    move-result v13

    .line 132
    if-eqz v13, :cond_2

    .line 133
    .line 134
    invoke-virtual {v6}, Ljava/lang/String;->length()I

    .line 135
    .line 136
    .line 137
    move-result v13

    .line 138
    add-int/lit8 v13, v13, -0x1

    .line 139
    .line 140
    const/4 v15, 0x1

    .line 141
    invoke-virtual {v6, v15, v13}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    .line 142
    .line 143
    .line 144
    move-result-object v6

    .line 145
    const-string v13, "substring(...)"

    .line 146
    .line 147
    invoke-static {v6, v13}, Lcom/github/catvod/spider/merge/C/f;->d(Ljava/lang/Object;Ljava/lang/String;)V

    .line 148
    .line 149
    .line 150
    goto :goto_3

    .line 151
    :cond_2
    const/4 v15, 0x1

    .line 152
    goto :goto_3

    .line 153
    :cond_3
    const/4 v15, 0x1

    .line 154
    const/4 v6, 0x0

    .line 155
    :goto_3
    add-int/lit8 v3, v3, 0x1

    .line 156
    .line 157
    const-string v13, "client_max_window_bits"

    .line 158
    .line 159
    invoke-static {v14, v13}, Lcom/github/catvod/spider/merge/H/r;->y(Ljava/lang/String;Ljava/lang/String;)Z

    .line 160
    .line 161
    .line 162
    move-result v13

    .line 163
    if-eqz v13, :cond_8

    .line 164
    .line 165
    if-eqz v7, :cond_4

    .line 166
    .line 167
    const/4 v11, 0x1

    .line 168
    :cond_4
    if-eqz v6, :cond_5

    .line 169
    .line 170
    invoke-static {v6}, Lcom/github/catvod/spider/merge/H/q;->v(Ljava/lang/String;)Ljava/lang/Integer;

    .line 171
    .line 172
    .line 173
    move-result-object v6

    .line 174
    move-object v7, v6

    .line 175
    goto :goto_4

    .line 176
    :cond_5
    const/4 v7, 0x0

    .line 177
    :goto_4
    if-nez v7, :cond_7

    .line 178
    .line 179
    :cond_6
    :goto_5
    move v14, v3

    .line 180
    const/4 v11, 0x1

    .line 181
    :goto_6
    const/16 v13, 0x3b

    .line 182
    .line 183
    const/16 v17, 0x1

    .line 184
    .line 185
    goto :goto_2

    .line 186
    :cond_7
    move v14, v3

    .line 187
    goto :goto_6

    .line 188
    :cond_8
    const-string v13, "client_no_context_takeover"

    .line 189
    .line 190
    invoke-static {v14, v13}, Lcom/github/catvod/spider/merge/H/r;->y(Ljava/lang/String;Ljava/lang/String;)Z

    .line 191
    .line 192
    .line 193
    move-result v13

    .line 194
    if-eqz v13, :cond_b

    .line 195
    .line 196
    if-eqz v8, :cond_9

    .line 197
    .line 198
    const/4 v11, 0x1

    .line 199
    :cond_9
    if-eqz v6, :cond_a

    .line 200
    .line 201
    const/4 v11, 0x1

    .line 202
    :cond_a
    move v14, v3

    .line 203
    const/4 v8, 0x1

    .line 204
    goto :goto_6

    .line 205
    :cond_b
    const-string v13, "server_max_window_bits"

    .line 206
    .line 207
    invoke-static {v14, v13}, Lcom/github/catvod/spider/merge/H/r;->y(Ljava/lang/String;Ljava/lang/String;)Z

    .line 208
    .line 209
    .line 210
    move-result v13

    .line 211
    if-eqz v13, :cond_e

    .line 212
    .line 213
    if-eqz v9, :cond_c

    .line 214
    .line 215
    const/4 v11, 0x1

    .line 216
    :cond_c
    if-eqz v6, :cond_d

    .line 217
    .line 218
    invoke-static {v6}, Lcom/github/catvod/spider/merge/H/q;->v(Ljava/lang/String;)Ljava/lang/Integer;

    .line 219
    .line 220
    .line 221
    move-result-object v6

    .line 222
    move-object v9, v6

    .line 223
    goto :goto_7

    .line 224
    :cond_d
    const/4 v9, 0x0

    .line 225
    :goto_7
    if-nez v9, :cond_7

    .line 226
    .line 227
    goto :goto_5

    .line 228
    :cond_e
    const-string v13, "server_no_context_takeover"

    .line 229
    .line 230
    invoke-static {v14, v13}, Lcom/github/catvod/spider/merge/H/r;->y(Ljava/lang/String;Ljava/lang/String;)Z

    .line 231
    .line 232
    .line 233
    move-result v13

    .line 234
    if-eqz v13, :cond_6

    .line 235
    .line 236
    if-eqz v10, :cond_f

    .line 237
    .line 238
    const/4 v11, 0x1

    .line 239
    :cond_f
    if-eqz v6, :cond_10

    .line 240
    .line 241
    const/4 v11, 0x1

    .line 242
    :cond_10
    move v14, v3

    .line 243
    const/4 v10, 0x1

    .line 244
    goto :goto_6

    .line 245
    :cond_11
    const/4 v15, 0x1

    .line 246
    const/4 v6, 0x1

    .line 247
    goto/16 :goto_1

    .line 248
    .line 249
    :cond_12
    move v14, v15

    .line 250
    const/4 v11, 0x1

    .line 251
    goto/16 :goto_1

    .line 252
    .line 253
    :cond_13
    :goto_8
    add-int/lit8 v4, v4, 0x1

    .line 254
    .line 255
    goto/16 :goto_0

    .line 256
    .line 257
    :cond_14
    new-instance v5, Lokhttp3/internal/ws/WebSocketExtensions;

    .line 258
    .line 259
    invoke-direct/range {v5 .. v11}, Lokhttp3/internal/ws/WebSocketExtensions;-><init>(ZLjava/lang/Integer;ZLjava/lang/Integer;ZZ)V

    .line 260
    .line 261
    .line 262
    return-object v5
.end method
