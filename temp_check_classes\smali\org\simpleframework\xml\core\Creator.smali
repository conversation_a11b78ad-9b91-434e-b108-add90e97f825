.class interface abstract Lorg/simpleframework/xml/core/Creator;
.super Ljava/lang/Object;
.source "SourceFile"


# virtual methods
.method public abstract getInstance()Ljava/lang/Object;
.end method

.method public abstract getInstance(Lorg/simpleframework/xml/core/Criteria;)Ljava/lang/Object;
.end method

.method public abstract getScore(Lorg/simpleframework/xml/core/Criteria;)D
.end method

.method public abstract getSignature()Lorg/simpleframework/xml/core/Signature;
.end method

.method public abstract getType()Ljava/lang/Class;
.end method
