.class public final Lcom/github/catvod/spider/merge/G/f;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Iterable;
.implements Lcom/github/catvod/spider/merge/D/a;


# instance fields
.field public final synthetic a:Lcom/github/catvod/spider/merge/G/h;


# direct methods
.method public constructor <init>(Lcom/github/catvod/spider/merge/G/h;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lcom/github/catvod/spider/merge/G/f;->a:Lcom/github/catvod/spider/merge/G/h;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final iterator()Ljava/util/Iterator;
    .locals 2

    .line 1
    iget-object v0, p0, Lcom/github/catvod/spider/merge/G/f;->a:Lcom/github/catvod/spider/merge/G/h;

    .line 2
    .line 3
    new-instance v1, Lcom/github/catvod/spider/merge/H/b;

    .line 4
    .line 5
    invoke-direct {v1, v0}, Lcom/github/catvod/spider/merge/H/b;-><init>(Lcom/github/catvod/spider/merge/G/h;)V

    .line 6
    .line 7
    .line 8
    return-object v1
.end method
