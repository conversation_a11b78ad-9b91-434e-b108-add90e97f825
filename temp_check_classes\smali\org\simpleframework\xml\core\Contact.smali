.class interface abstract Lorg/simpleframework/xml/core/Contact;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lorg/simpleframework/xml/strategy/Type;


# virtual methods
.method public abstract get(Ljava/lang/Object;)Ljava/lang/Object;
.end method

.method public abstract getAnnotation()Ljava/lang/annotation/Annotation;
.end method

.method public abstract getDeclaringClass()Ljava/lang/Class;
.end method

.method public abstract getDependent()Ljava/lang/Class;
.end method

.method public abstract getDependents()[Ljava/lang/Class;
.end method

.method public abstract getName()Ljava/lang/String;
.end method

.method public abstract isReadOnly()Z
.end method

.method public abstract set(Ljava/lang/Object;Ljava/lang/Object;)V
.end method

.method public abstract toString()Ljava/lang/String;
.end method
