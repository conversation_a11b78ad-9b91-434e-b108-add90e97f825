.class public Lcom/github/catvod/spider/merge/a/h;
.super Ljava/lang/Object;
.source "SourceFile"


# instance fields
.field private a:Ljava/lang/String;
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "type_name"
    .end annotation
.end field

.field private b:Ljava/lang/String;
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "vod_id"
    .end annotation
.end field

.field private c:Ljava/lang/String;
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "vod_name"
    .end annotation
.end field

.field private d:Ljava/lang/String;
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "vod_pic"
    .end annotation
.end field

.field private e:Ljava/lang/String;
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "vod_remarks"
    .end annotation
.end field

.field private f:Ljava/lang/String;
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "vod_year"
    .end annotation
.end field

.field private g:Ljava/lang/String;
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "vod_area"
    .end annotation
.end field

.field private h:Ljava/lang/String;
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "vod_actor"
    .end annotation
.end field

.field private i:Ljava/lang/String;
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "vod_director"
    .end annotation
.end field

.field private j:Ljava/lang/String;
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "vod_content"
    .end annotation
.end field

.field private k:Ljava/lang/String;
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "vod_play_from"
    .end annotation
.end field

.field private l:Ljava/lang/String;
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "vod_play_url"
    .end annotation
.end field

.field private m:Ljava/lang/String;
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "vod_tag"
    .end annotation
.end field

.field private n:Ljava/lang/String;
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "action"
    .end annotation
.end field

.field private o:Lcom/github/catvod/spider/merge/a/g;
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "style"
    .end annotation
.end field


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p1, p0, Lcom/github/catvod/spider/merge/a/h;->b:Ljava/lang/String;

    .line 4
    iput-object p2, p0, Lcom/github/catvod/spider/merge/a/h;->c:Ljava/lang/String;

    .line 5
    iput-object p3, p0, Lcom/github/catvod/spider/merge/a/h;->d:Ljava/lang/String;

    return-void
.end method
