.class interface abstract Lorg/simpleframework/xml/core/Label;
.super Ljava/lang/Object;
.source "SourceFile"


# virtual methods
.method public abstract getAnnotation()Ljava/lang/annotation/Annotation;
.end method

.method public abstract getContact()Lorg/simpleframework/xml/core/Contact;
.end method

.method public abstract getConverter(Lorg/simpleframework/xml/core/Context;)Lorg/simpleframework/xml/core/Converter;
.end method

.method public abstract getDecorator()Lorg/simpleframework/xml/core/Decorator;
.end method

.method public abstract getDependent()Lorg/simpleframework/xml/strategy/Type;
.end method

.method public abstract getEmpty(Lorg/simpleframework/xml/core/Context;)Ljava/lang/Object;
.end method

.method public abstract getEntry()Ljava/lang/String;
.end method

.method public abstract getExpression()Lorg/simpleframework/xml/core/Expression;
.end method

.method public abstract getKey()Ljava/lang/Object;
.end method

.method public abstract getLabel(Ljava/lang/Class;)Lorg/simpleframework/xml/core/Label;
.end method

.method public abstract getName()Ljava/lang/String;
.end method

.method public abstract getNames()[Ljava/lang/String;
.end method

.method public abstract getOverride()Ljava/lang/String;
.end method

.method public abstract getPath()Ljava/lang/String;
.end method

.method public abstract getPaths()[Ljava/lang/String;
.end method

.method public abstract getType()Ljava/lang/Class;
.end method

.method public abstract getType(Ljava/lang/Class;)Lorg/simpleframework/xml/strategy/Type;
.end method

.method public abstract isAttribute()Z
.end method

.method public abstract isCollection()Z
.end method

.method public abstract isData()Z
.end method

.method public abstract isInline()Z
.end method

.method public abstract isRequired()Z
.end method

.method public abstract isText()Z
.end method

.method public abstract isTextList()Z
.end method

.method public abstract isUnion()Z
.end method

.method public abstract toString()Ljava/lang/String;
.end method
