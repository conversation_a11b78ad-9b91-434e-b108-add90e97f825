.class public final Lcom/github/catvod/spider/merge/G/h;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/github/catvod/spider/merge/G/b;


# instance fields
.field public final synthetic a:I

.field public final b:Lcom/github/catvod/spider/merge/C/g;

.field public final c:Ljava/lang/Object;


# direct methods
.method public constructor <init>(Lcom/github/catvod/spider/merge/G/b;Lcom/github/catvod/spider/merge/B/l;)V
    .locals 1

    const/4 v0, 0x0

    iput v0, p0, Lcom/github/catvod/spider/merge/G/h;->a:I

    const-string v0, "sequence"

    invoke-static {p1, v0}, Lcom/github/catvod/spider/merge/C/f;->e(Ljava/lang/Object;Ljava/lang/String;)V

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/github/catvod/spider/merge/G/h;->c:Ljava/lang/Object;

    check-cast p2, Lcom/github/catvod/spider/merge/C/g;

    iput-object p2, p0, Lcom/github/catvod/spider/merge/G/h;->b:Lcom/github/catvod/spider/merge/C/g;

    return-void
.end method

.method public constructor <init>(Ljava/lang/CharSequence;Lcom/github/catvod/spider/merge/B/p;)V
    .locals 1

    const/4 v0, 0x1

    iput v0, p0, Lcom/github/catvod/spider/merge/G/h;->a:I

    const-string v0, "input"

    invoke-static {p1, v0}, Lcom/github/catvod/spider/merge/C/f;->e(Ljava/lang/Object;Ljava/lang/String;)V

    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    iput-object p1, p0, Lcom/github/catvod/spider/merge/G/h;->c:Ljava/lang/Object;

    .line 5
    check-cast p2, Lcom/github/catvod/spider/merge/C/g;

    iput-object p2, p0, Lcom/github/catvod/spider/merge/G/h;->b:Lcom/github/catvod/spider/merge/C/g;

    return-void
.end method


# virtual methods
.method public final iterator()Ljava/util/Iterator;
    .locals 1

    .line 1
    iget v0, p0, Lcom/github/catvod/spider/merge/G/h;->a:I

    .line 2
    .line 3
    packed-switch v0, :pswitch_data_0

    .line 4
    .line 5
    .line 6
    new-instance v0, Lcom/github/catvod/spider/merge/H/b;

    .line 7
    .line 8
    invoke-direct {v0, p0}, Lcom/github/catvod/spider/merge/H/b;-><init>(Lcom/github/catvod/spider/merge/G/h;)V

    .line 9
    .line 10
    .line 11
    return-object v0

    .line 12
    :pswitch_0
    new-instance v0, Lcom/github/catvod/spider/merge/G/g;

    .line 13
    .line 14
    invoke-direct {v0, p0}, Lcom/github/catvod/spider/merge/G/g;-><init>(Lcom/github/catvod/spider/merge/G/h;)V

    .line 15
    .line 16
    .line 17
    return-object v0

    .line 18
    nop

    .line 19
    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_0
    .end packed-switch
.end method
