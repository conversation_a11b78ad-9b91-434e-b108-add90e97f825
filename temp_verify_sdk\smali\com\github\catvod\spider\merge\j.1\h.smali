.class public abstract Lcom/github/catvod/spider/merge/j/h;
.super Ljava/lang/Object;
.source "SourceFile"


# static fields
.field public static final a:Z

.field public static final b:Lcom/github/catvod/spider/merge/j/g;

.field public static final c:Lcom/github/catvod/spider/merge/j/g;

.field public static final d:Lcom/github/catvod/spider/merge/j/a;

.field public static final e:Lcom/github/catvod/spider/merge/j/c;

.field public static final f:Lcom/github/catvod/spider/merge/j/e;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    :try_start_0
    const-string v0, "java.sql.Date"

    .line 2
    .line 3
    invoke-static {v0}, Ljava/lang/Class;->forName(Ljava/lang/String;)Ljava/lang/Class;
    :try_end_0
    .catch Ljava/lang/ClassNotFoundException; {:try_start_0 .. :try_end_0} :catch_0

    .line 4
    .line 5
    .line 6
    const/4 v0, 0x1

    .line 7
    goto :goto_0

    .line 8
    :catch_0
    const/4 v0, 0x0

    .line 9
    :goto_0
    sput-boolean v0, Lcom/github/catvod/spider/merge/j/h;->a:Z

    .line 10
    .line 11
    if-eqz v0, :cond_0

    .line 12
    .line 13
    new-instance v0, Lcom/github/catvod/spider/merge/j/g;

    .line 14
    .line 15
    const-class v1, Ljava/sql/Date;

    .line 16
    .line 17
    invoke-direct {v0, v1}, Lcom/github/catvod/spider/merge/h/g;-><init>(Ljava/lang/Class;)V

    .line 18
    .line 19
    .line 20
    sput-object v0, Lcom/github/catvod/spider/merge/j/h;->b:Lcom/github/catvod/spider/merge/j/g;

    .line 21
    .line 22
    new-instance v0, Lcom/github/catvod/spider/merge/j/g;

    .line 23
    .line 24
    const-class v1, Ljava/sql/Timestamp;

    .line 25
    .line 26
    invoke-direct {v0, v1}, Lcom/github/catvod/spider/merge/h/g;-><init>(Ljava/lang/Class;)V

    .line 27
    .line 28
    .line 29
    sput-object v0, Lcom/github/catvod/spider/merge/j/h;->c:Lcom/github/catvod/spider/merge/j/g;

    .line 30
    .line 31
    sget-object v0, Lcom/github/catvod/spider/merge/j/b;->b:Lcom/github/catvod/spider/merge/j/a;

    .line 32
    .line 33
    sput-object v0, Lcom/github/catvod/spider/merge/j/h;->d:Lcom/github/catvod/spider/merge/j/a;

    .line 34
    .line 35
    sget-object v0, Lcom/github/catvod/spider/merge/j/d;->b:Lcom/github/catvod/spider/merge/j/c;

    .line 36
    .line 37
    sput-object v0, Lcom/github/catvod/spider/merge/j/h;->e:Lcom/github/catvod/spider/merge/j/c;

    .line 38
    .line 39
    sget-object v0, Lcom/github/catvod/spider/merge/j/f;->b:Lcom/github/catvod/spider/merge/j/e;

    .line 40
    .line 41
    sput-object v0, Lcom/github/catvod/spider/merge/j/h;->f:Lcom/github/catvod/spider/merge/j/e;

    .line 42
    .line 43
    goto :goto_1

    .line 44
    :cond_0
    const/4 v0, 0x0

    .line 45
    sput-object v0, Lcom/github/catvod/spider/merge/j/h;->b:Lcom/github/catvod/spider/merge/j/g;

    .line 46
    .line 47
    sput-object v0, Lcom/github/catvod/spider/merge/j/h;->c:Lcom/github/catvod/spider/merge/j/g;

    .line 48
    .line 49
    sput-object v0, Lcom/github/catvod/spider/merge/j/h;->d:Lcom/github/catvod/spider/merge/j/a;

    .line 50
    .line 51
    sput-object v0, Lcom/github/catvod/spider/merge/j/h;->e:Lcom/github/catvod/spider/merge/j/c;

    .line 52
    .line 53
    sput-object v0, Lcom/github/catvod/spider/merge/j/h;->f:Lcom/github/catvod/spider/merge/j/e;

    .line 54
    .line 55
    :goto_1
    return-void
.end method
