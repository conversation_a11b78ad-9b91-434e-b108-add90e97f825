.class public Lcom/github/catvod/spider/merge/h/W;
.super Lcom/github/catvod/spider/merge/e/z;
.source "SourceFile"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public final a(Lcom/github/catvod/spider/merge/l/a;Ljava/lang/Object;)V
    .locals 0

    .line 1
    check-cast p2, Ljava/lang/Boolean;

    .line 2
    .line 3
    if-nez p2, :cond_0

    .line 4
    .line 5
    invoke-virtual {p1}, Lcom/github/catvod/spider/merge/l/a;->i()Lcom/github/catvod/spider/merge/l/a;

    .line 6
    .line 7
    .line 8
    goto :goto_1

    .line 9
    :cond_0
    invoke-virtual {p1}, Lcom/github/catvod/spider/merge/l/a;->s()V

    .line 10
    .line 11
    .line 12
    invoke-virtual {p1}, Lcom/github/catvod/spider/merge/l/a;->a()V

    .line 13
    .line 14
    .line 15
    invoke-virtual {p2}, Ljava/lang/<PERSON>;->booleanValue()Z

    .line 16
    .line 17
    .line 18
    move-result p2

    .line 19
    if-eqz p2, :cond_1

    .line 20
    .line 21
    const-string p2, "true"

    .line 22
    .line 23
    goto :goto_0

    .line 24
    :cond_1
    const-string p2, "false"

    .line 25
    .line 26
    :goto_0
    iget-object p1, p1, Lcom/github/catvod/spider/merge/l/a;->a:Ljava/io/Writer;

    .line 27
    .line 28
    invoke-virtual {p1, p2}, Ljava/io/Writer;->write(Ljava/lang/String;)V

    .line 29
    .line 30
    .line 31
    :goto_1
    return-void
.end method
