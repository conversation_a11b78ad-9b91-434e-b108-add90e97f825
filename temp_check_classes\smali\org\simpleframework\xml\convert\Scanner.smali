.class interface abstract Lorg/simpleframework/xml/convert/Scanner;
.super Ljava/lang/Object;
.source "SourceFile"


# virtual methods
.method public abstract scan(Ljava/lang/Class;)Ljava/lang/annotation/Annotation;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T::",
            "Ljava/lang/annotation/Annotation;",
            ">(",
            "Ljava/lang/Class<",
            "TT;>;)TT;"
        }
    .end annotation
.end method
