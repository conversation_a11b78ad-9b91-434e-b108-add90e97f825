.class public final Lokhttp3/internal/_RequestBodyCommonKt$commonToRequestBody$1;
.super Lokhttp3/RequestBody;
.source "SourceFile"


# instance fields
.field final synthetic $byteCount:I

.field final synthetic $contentType:Lokhttp3/MediaType;

.field final synthetic $offset:I

.field final synthetic $this_commonToRequestBody:[B


# direct methods
.method public constructor <init>(Lokhttp3/MediaType;I[BI)V
    .locals 0

    .line 1
    iput-object p1, p0, Lokhttp3/internal/_RequestBodyCommonKt$commonToRequestBody$1;->$contentType:Lokhttp3/MediaType;

    .line 2
    .line 3
    iput p2, p0, Lokhttp3/internal/_RequestBodyCommonKt$commonToRequestBody$1;->$byteCount:I

    .line 4
    .line 5
    iput-object p3, p0, Lokhttp3/internal/_RequestBodyCommonKt$commonToRequestBody$1;->$this_commonToRequestBody:[B

    .line 6
    .line 7
    iput p4, p0, Lokhttp3/internal/_RequestBodyCommonKt$commonToRequestBody$1;->$offset:I

    .line 8
    .line 9
    invoke-direct {p0}, Lokhttp3/RequestBody;-><init>()V

    .line 10
    .line 11
    .line 12
    return-void
.end method


# virtual methods
.method public contentLength()J
    .locals 2

    .line 1
    iget v0, p0, Lokhttp3/internal/_RequestBodyCommonKt$commonToRequestBody$1;->$byteCount:I

    .line 2
    .line 3
    int-to-long v0, v0

    .line 4
    return-wide v0
.end method

.method public contentType()Lokhttp3/MediaType;
    .locals 1

    .line 1
    iget-object v0, p0, Lokhttp3/internal/_RequestBodyCommonKt$commonToRequestBody$1;->$contentType:Lokhttp3/MediaType;

    .line 2
    .line 3
    return-object v0
.end method

.method public writeTo(Lokio/BufferedSink;)V
    .locals 3

    .line 1
    const-string v0, "sink"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lcom/github/catvod/spider/merge/C/f;->e(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lokhttp3/internal/_RequestBodyCommonKt$commonToRequestBody$1;->$this_commonToRequestBody:[B

    .line 7
    .line 8
    iget v1, p0, Lokhttp3/internal/_RequestBodyCommonKt$commonToRequestBody$1;->$offset:I

    .line 9
    .line 10
    iget v2, p0, Lokhttp3/internal/_RequestBodyCommonKt$commonToRequestBody$1;->$byteCount:I

    .line 11
    .line 12
    invoke-interface {p1, v0, v1, v2}, Lokio/BufferedSink;->write([BII)Lokio/BufferedSink;

    .line 13
    .line 14
    .line 15
    return-void
.end method
