.class public abstract Lcom/github/catvod/spider/merge/H/k;
.super Lcom/github/catvod/spider/merge/A/a;
.source "SourceFile"


# direct methods
.method public static u(Ljava/lang/String;)Ljava/lang/String;
    .locals 14

    .line 1
    const-string v0, "<this>"

    .line 2
    .line 3
    invoke-static {p0, v0}, Lcom/github/catvod/spider/merge/C/f;->e(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    const-string v0, "|"

    .line 7
    .line 8
    invoke-static {v0}, Lcom/github/catvod/spider/merge/H/r;->z(Ljava/lang/String;)Z

    .line 9
    .line 10
    .line 11
    move-result v1

    .line 12
    if-nez v1, :cond_e

    .line 13
    .line 14
    const-string v1, "\r\n"

    .line 15
    .line 16
    const-string v2, "\n"

    .line 17
    .line 18
    const-string v3, "\r"

    .line 19
    .line 20
    filled-new-array {v1, v2, v3}, [Ljava/lang/String;

    .line 21
    .line 22
    .line 23
    move-result-object v1

    .line 24
    invoke-static {v1}, Lcom/github/catvod/spider/merge/q/i;->u([Ljava/lang/Object;)Ljava/util/List;

    .line 25
    .line 26
    .line 27
    move-result-object v1

    .line 28
    new-instance v2, Lcom/github/catvod/spider/merge/G/h;

    .line 29
    .line 30
    new-instance v3, Lcom/github/catvod/spider/merge/H/s;

    .line 31
    .line 32
    const/4 v4, 0x1

    .line 33
    invoke-direct {v3, v4, v1}, Lcom/github/catvod/spider/merge/H/s;-><init>(ILjava/lang/Object;)V

    .line 34
    .line 35
    .line 36
    invoke-direct {v2, p0, v3}, Lcom/github/catvod/spider/merge/G/h;-><init>(Ljava/lang/CharSequence;Lcom/github/catvod/spider/merge/B/p;)V

    .line 37
    .line 38
    .line 39
    new-instance v1, Lcom/github/catvod/spider/merge/H/f;

    .line 40
    .line 41
    const/4 v3, 0x1

    .line 42
    invoke-direct {v1, v3, p0}, Lcom/github/catvod/spider/merge/H/f;-><init>(ILjava/lang/Object;)V

    .line 43
    .line 44
    .line 45
    invoke-interface {v2}, Lcom/github/catvod/spider/merge/G/b;->iterator()Ljava/util/Iterator;

    .line 46
    .line 47
    .line 48
    move-result-object v2

    .line 49
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 50
    .line 51
    .line 52
    move-result v3

    .line 53
    if-nez v3, :cond_0

    .line 54
    .line 55
    sget-object v1, Lcom/github/catvod/spider/merge/q/r;->a:Lcom/github/catvod/spider/merge/q/r;

    .line 56
    .line 57
    goto :goto_1

    .line 58
    :cond_0
    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 59
    .line 60
    .line 61
    move-result-object v3

    .line 62
    invoke-interface {v1, v3}, Lcom/github/catvod/spider/merge/B/l;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 63
    .line 64
    .line 65
    move-result-object v3

    .line 66
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 67
    .line 68
    .line 69
    move-result v4

    .line 70
    if-nez v4, :cond_1

    .line 71
    .line 72
    invoke-static {v3}, Lcom/github/catvod/spider/merge/A/a;->q(Ljava/lang/Object;)Ljava/util/List;

    .line 73
    .line 74
    .line 75
    move-result-object v1

    .line 76
    goto :goto_1

    .line 77
    :cond_1
    new-instance v4, Ljava/util/ArrayList;

    .line 78
    .line 79
    invoke-direct {v4}, Ljava/util/ArrayList;-><init>()V

    .line 80
    .line 81
    .line 82
    invoke-virtual {v4, v3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 83
    .line 84
    .line 85
    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 86
    .line 87
    .line 88
    move-result v3

    .line 89
    if-eqz v3, :cond_2

    .line 90
    .line 91
    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 92
    .line 93
    .line 94
    move-result-object v3

    .line 95
    invoke-interface {v1, v3}, Lcom/github/catvod/spider/merge/B/l;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    .line 96
    .line 97
    .line 98
    move-result-object v3

    .line 99
    invoke-virtual {v4, v3}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 100
    .line 101
    .line 102
    goto :goto_0

    .line 103
    :cond_2
    move-object v1, v4

    .line 104
    :goto_1
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    .line 105
    .line 106
    .line 107
    move-result p0

    .line 108
    invoke-interface {v1}, Ljava/util/List;->size()I

    .line 109
    .line 110
    .line 111
    invoke-interface {v1}, Ljava/util/List;->size()I

    .line 112
    .line 113
    .line 114
    move-result v2

    .line 115
    const/4 v3, 0x1

    .line 116
    sub-int/2addr v2, v3

    .line 117
    new-instance v4, Ljava/util/ArrayList;

    .line 118
    .line 119
    invoke-direct {v4}, Ljava/util/ArrayList;-><init>()V

    .line 120
    .line 121
    .line 122
    invoke-interface {v1}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 123
    .line 124
    .line 125
    move-result-object v1

    .line 126
    const/4 v5, 0x0

    .line 127
    const/4 v6, 0x0

    .line 128
    :goto_2
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 129
    .line 130
    .line 131
    move-result v7

    .line 132
    if-eqz v7, :cond_d

    .line 133
    .line 134
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 135
    .line 136
    .line 137
    move-result-object v7

    .line 138
    add-int/lit8 v8, v6, 0x1

    .line 139
    .line 140
    if-ltz v6, :cond_c

    .line 141
    .line 142
    check-cast v7, Ljava/lang/String;

    .line 143
    .line 144
    const/4 v9, 0x0

    .line 145
    if-eqz v6, :cond_3

    .line 146
    .line 147
    if-ne v6, v2, :cond_4

    .line 148
    .line 149
    :cond_3
    invoke-static {v7}, Lcom/github/catvod/spider/merge/H/r;->z(Ljava/lang/String;)Z

    .line 150
    .line 151
    .line 152
    move-result v6

    .line 153
    if-eqz v6, :cond_4

    .line 154
    .line 155
    :goto_3
    move-object v7, v9

    .line 156
    goto :goto_6

    .line 157
    :cond_4
    invoke-virtual {v7}, Ljava/lang/String;->length()I

    .line 158
    .line 159
    .line 160
    move-result v6

    .line 161
    const/4 v10, 0x0

    .line 162
    :goto_4
    const/4 v11, -0x1

    .line 163
    if-ge v10, v6, :cond_6

    .line 164
    .line 165
    invoke-virtual {v7, v10}, Ljava/lang/String;->charAt(I)C

    .line 166
    .line 167
    .line 168
    move-result v12

    .line 169
    invoke-static {v12}, Ljava/lang/Character;->isWhitespace(C)Z

    .line 170
    .line 171
    .line 172
    move-result v13

    .line 173
    if-nez v13, :cond_5

    .line 174
    .line 175
    invoke-static {v12}, Ljava/lang/Character;->isSpaceChar(C)Z

    .line 176
    .line 177
    .line 178
    move-result v12

    .line 179
    if-eqz v12, :cond_7

    .line 180
    .line 181
    :cond_5
    add-int/lit8 v10, v10, 0x1

    .line 182
    .line 183
    goto :goto_4

    .line 184
    :cond_6
    const/4 v10, -0x1

    .line 185
    :cond_7
    if-ne v10, v11, :cond_8

    .line 186
    .line 187
    goto :goto_5

    .line 188
    :cond_8
    invoke-static {v7, v0, v10, v5}, Lcom/github/catvod/spider/merge/H/r;->D(Ljava/lang/String;Ljava/lang/String;IZ)Z

    .line 189
    .line 190
    .line 191
    move-result v6

    .line 192
    if-eqz v6, :cond_9

    .line 193
    .line 194
    add-int/2addr v10, v3

    .line 195
    invoke-virtual {v7, v10}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    .line 196
    .line 197
    .line 198
    move-result-object v9

    .line 199
    const-string v6, "substring(...)"

    .line 200
    .line 201
    invoke-static {v9, v6}, Lcom/github/catvod/spider/merge/C/f;->d(Ljava/lang/Object;Ljava/lang/String;)V

    .line 202
    .line 203
    .line 204
    :cond_9
    :goto_5
    if-eqz v9, :cond_a

    .line 205
    .line 206
    goto :goto_3

    .line 207
    :cond_a
    :goto_6
    if-eqz v7, :cond_b

    .line 208
    .line 209
    invoke-virtual {v4, v7}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    .line 210
    .line 211
    .line 212
    :cond_b
    move v6, v8

    .line 213
    goto :goto_2

    .line 214
    :cond_c
    new-instance p0, Ljava/lang/ArithmeticException;

    .line 215
    .line 216
    const-string v0, "Index overflow has happened."

    .line 217
    .line 218
    invoke-direct {p0, v0}, Ljava/lang/ArithmeticException;-><init>(Ljava/lang/String;)V

    .line 219
    .line 220
    .line 221
    throw p0

    .line 222
    :cond_d
    new-instance v5, Ljava/lang/StringBuilder;

    .line 223
    .line 224
    invoke-direct {v5, p0}, Ljava/lang/StringBuilder;-><init>(I)V

    .line 225
    .line 226
    .line 227
    const-string v9, "..."

    .line 228
    .line 229
    const/4 v10, 0x0

    .line 230
    const-string v6, "\n"

    .line 231
    .line 232
    const-string v7, ""

    .line 233
    .line 234
    move-object v8, v7

    .line 235
    invoke-static/range {v4 .. v10}, Lcom/github/catvod/spider/merge/q/j;->A(Ljava/util/Collection;Ljava/lang/StringBuilder;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Lcom/github/catvod/spider/merge/B/l;)V

    .line 236
    .line 237
    .line 238
    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 239
    .line 240
    .line 241
    move-result-object p0

    .line 242
    const-string v0, "toString(...)"

    .line 243
    .line 244
    invoke-static {p0, v0}, Lcom/github/catvod/spider/merge/C/f;->d(Ljava/lang/Object;Ljava/lang/String;)V

    .line 245
    .line 246
    .line 247
    return-object p0

    .line 248
    :cond_e
    new-instance p0, Ljava/lang/IllegalArgumentException;

    .line 249
    .line 250
    const-string v0, "marginPrefix must be non-blank string."

    .line 251
    .line 252
    invoke-direct {p0, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 253
    .line 254
    .line 255
    throw p0
.end method
