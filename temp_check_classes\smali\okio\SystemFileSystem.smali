.class public final Lokio/SystemFileSystem;
.super Ljava/lang/Object;
.source "SourceFile"


# direct methods
.method public static final synthetic getSYSTEM(Lokio/FileSystem$Companion;)Lokio/FileSystem;
    .locals 1

    .line 1
    const-string v0, "<this>"

    .line 2
    .line 3
    invoke-static {p0, v0}, Lcom/github/catvod/spider/merge/C/f;->e(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    sget-object p0, Lokio/FileSystem;->SYSTEM:Lokio/FileSystem;

    .line 7
    .line 8
    return-object p0
.end method

.method public static synthetic getSYSTEM$annotations(Lokio/FileSystem$Companion;)V
    .locals 0

    return-void
.end method
