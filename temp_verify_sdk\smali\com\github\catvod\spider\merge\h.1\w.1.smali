.class public final Lcom/github/catvod/spider/merge/h/w;
.super Lcom/github/catvod/spider/merge/e/z;
.source "SourceFile"


# instance fields
.field public final synthetic a:I

.field public final b:Ljava/lang/Object;

.field public final c:Ljava/lang/Object;

.field public final d:Ljava/lang/Object;


# direct methods
.method public constructor <init>(Lcom/github/catvod/spider/merge/e/n;Lcom/github/catvod/spider/merge/e/z;Ljava/lang/reflect/Type;)V
    .locals 1

    const/4 v0, 0x0

    iput v0, p0, Lcom/github/catvod/spider/merge/h/w;->a:I

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/github/catvod/spider/merge/h/w;->b:Ljava/lang/Object;

    .line 3
    iput-object p2, p0, Lcom/github/catvod/spider/merge/h/w;->c:Ljava/lang/Object;

    .line 4
    iput-object p3, p0, Lcom/github/catvod/spider/merge/h/w;->d:Ljava/lang/Object;

    return-void
.end method

.method public constructor <init>(Ljava/lang/Class;)V
    .locals 11

    const/4 v0, 0x1

    iput v0, p0, Lcom/github/catvod/spider/merge/h/w;->a:I

    .line 5
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 6
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lcom/github/catvod/spider/merge/h/w;->b:Ljava/lang/Object;

    .line 7
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lcom/github/catvod/spider/merge/h/w;->c:Ljava/lang/Object;

    .line 8
    new-instance v0, Ljava/util/HashMap;

    invoke-direct {v0}, Ljava/util/HashMap;-><init>()V

    iput-object v0, p0, Lcom/github/catvod/spider/merge/h/w;->d:Ljava/lang/Object;

    .line 9
    :try_start_0
    new-instance v0, Lcom/github/catvod/spider/merge/h/d0;

    invoke-direct {v0, p1}, Lcom/github/catvod/spider/merge/h/d0;-><init>(Ljava/lang/Class;)V

    .line 10
    invoke-static {v0}, Ljava/security/AccessController;->doPrivileged(Ljava/security/PrivilegedAction;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, [Ljava/lang/reflect/Field;

    .line 11
    array-length v0, p1

    const/4 v1, 0x0

    const/4 v2, 0x0

    :goto_0
    if-ge v2, v0, :cond_1

    aget-object v3, p1, v2

    const/4 v4, 0x0

    .line 12
    invoke-virtual {v3, v4}, Ljava/lang/reflect/Field;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/Enum;

    .line 13
    invoke-virtual {v4}, Ljava/lang/Enum;->name()Ljava/lang/String;

    move-result-object v5

    .line 14
    invoke-virtual {v4}, Ljava/lang/Enum;->toString()Ljava/lang/String;

    move-result-object v6

    .line 15
    const-class v7, Lcom/github/catvod/spider/merge/f/b;

    invoke-virtual {v3, v7}, Ljava/lang/reflect/Field;->getAnnotation(Ljava/lang/Class;)Ljava/lang/annotation/Annotation;

    move-result-object v3

    check-cast v3, Lcom/github/catvod/spider/merge/f/b;

    if-eqz v3, :cond_0

    .line 16
    invoke-interface {v3}, Lcom/github/catvod/spider/merge/f/b;->value()Ljava/lang/String;

    move-result-object v5

    .line 17
    invoke-interface {v3}, Lcom/github/catvod/spider/merge/f/b;->alternate()[Ljava/lang/String;

    move-result-object v3

    array-length v7, v3

    const/4 v8, 0x0

    :goto_1
    if-ge v8, v7, :cond_0

    aget-object v9, v3, v8

    .line 18
    iget-object v10, p0, Lcom/github/catvod/spider/merge/h/w;->b:Ljava/lang/Object;

    check-cast v10, Ljava/util/HashMap;

    invoke-virtual {v10, v9, v4}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    add-int/lit8 v8, v8, 0x1

    goto :goto_1

    :catch_0
    move-exception p1

    goto :goto_2

    .line 19
    :cond_0
    iget-object v3, p0, Lcom/github/catvod/spider/merge/h/w;->b:Ljava/lang/Object;

    check-cast v3, Ljava/util/HashMap;

    invoke-virtual {v3, v5, v4}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 20
    iget-object v3, p0, Lcom/github/catvod/spider/merge/h/w;->c:Ljava/lang/Object;

    check-cast v3, Ljava/util/HashMap;

    invoke-virtual {v3, v6, v4}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 21
    iget-object v3, p0, Lcom/github/catvod/spider/merge/h/w;->d:Ljava/lang/Object;

    check-cast v3, Ljava/util/HashMap;

    invoke-virtual {v3, v4, v5}, Ljava/util/HashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    :try_end_0
    .catch Ljava/lang/IllegalAccessException; {:try_start_0 .. :try_end_0} :catch_0

    add-int/lit8 v2, v2, 0x1

    goto :goto_0

    :cond_1
    return-void

    .line 22
    :goto_2
    new-instance v0, Ljava/lang/AssertionError;

    invoke-direct {v0, p1}, Ljava/lang/AssertionError;-><init>(Ljava/lang/Object;)V

    throw v0
.end method


# virtual methods
.method public final a(Lcom/github/catvod/spider/merge/l/a;Ljava/lang/Object;)V
    .locals 4

    .line 1
    iget v0, p0, Lcom/github/catvod/spider/merge/h/w;->a:I

    .line 2
    .line 3
    packed-switch v0, :pswitch_data_0

    .line 4
    .line 5
    .line 6
    check-cast p2, Ljava/lang/Enum;

    .line 7
    .line 8
    if-nez p2, :cond_0

    .line 9
    .line 10
    const/4 p2, 0x0

    .line 11
    goto :goto_0

    .line 12
    :cond_0
    iget-object v0, p0, Lcom/github/catvod/spider/merge/h/w;->d:Ljava/lang/Object;

    .line 13
    .line 14
    check-cast v0, Ljava/util/HashMap;

    .line 15
    .line 16
    invoke-virtual {v0, p2}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    move-result-object p2

    .line 20
    check-cast p2, Ljava/lang/String;

    .line 21
    .line 22
    :goto_0
    invoke-virtual {p1, p2}, Lcom/github/catvod/spider/merge/l/a;->q(Ljava/lang/String;)V

    .line 23
    .line 24
    .line 25
    return-void

    .line 26
    :pswitch_0
    iget-object v0, p0, Lcom/github/catvod/spider/merge/h/w;->d:Ljava/lang/Object;

    .line 27
    .line 28
    check-cast v0, Ljava/lang/reflect/Type;

    .line 29
    .line 30
    if-eqz p2, :cond_2

    .line 31
    .line 32
    instance-of v1, v0, Ljava/lang/Class;

    .line 33
    .line 34
    if-nez v1, :cond_1

    .line 35
    .line 36
    instance-of v1, v0, Ljava/lang/reflect/TypeVariable;

    .line 37
    .line 38
    if-eqz v1, :cond_2

    .line 39
    .line 40
    :cond_1
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 41
    .line 42
    .line 43
    move-result-object v1

    .line 44
    goto :goto_1

    .line 45
    :cond_2
    move-object v1, v0

    .line 46
    :goto_1
    iget-object v2, p0, Lcom/github/catvod/spider/merge/h/w;->c:Ljava/lang/Object;

    .line 47
    .line 48
    check-cast v2, Lcom/github/catvod/spider/merge/e/z;

    .line 49
    .line 50
    if-eq v1, v0, :cond_7

    .line 51
    .line 52
    new-instance v0, Lcom/github/catvod/spider/merge/k/a;

    .line 53
    .line 54
    invoke-direct {v0, v1}, Lcom/github/catvod/spider/merge/k/a;-><init>(Ljava/lang/reflect/Type;)V

    .line 55
    .line 56
    .line 57
    iget-object v1, p0, Lcom/github/catvod/spider/merge/h/w;->b:Ljava/lang/Object;

    .line 58
    .line 59
    check-cast v1, Lcom/github/catvod/spider/merge/e/n;

    .line 60
    .line 61
    invoke-virtual {v1, v0}, Lcom/github/catvod/spider/merge/e/n;->b(Lcom/github/catvod/spider/merge/k/a;)Lcom/github/catvod/spider/merge/e/z;

    .line 62
    .line 63
    .line 64
    move-result-object v0

    .line 65
    instance-of v1, v0, Lcom/github/catvod/spider/merge/h/q;

    .line 66
    .line 67
    if-nez v1, :cond_3

    .line 68
    .line 69
    goto :goto_4

    .line 70
    :cond_3
    move-object v1, v2

    .line 71
    :goto_2
    instance-of v3, v1, Lcom/github/catvod/spider/merge/h/v;

    .line 72
    .line 73
    if-eqz v3, :cond_5

    .line 74
    .line 75
    move-object v3, v1

    .line 76
    check-cast v3, Lcom/github/catvod/spider/merge/h/v;

    .line 77
    .line 78
    invoke-virtual {v3}, Lcom/github/catvod/spider/merge/h/v;->b()Lcom/github/catvod/spider/merge/e/z;

    .line 79
    .line 80
    .line 81
    move-result-object v3

    .line 82
    if-ne v3, v1, :cond_4

    .line 83
    .line 84
    goto :goto_3

    .line 85
    :cond_4
    move-object v1, v3

    .line 86
    goto :goto_2

    .line 87
    :cond_5
    :goto_3
    instance-of v1, v1, Lcom/github/catvod/spider/merge/h/q;

    .line 88
    .line 89
    if-nez v1, :cond_6

    .line 90
    .line 91
    goto :goto_5

    .line 92
    :cond_6
    :goto_4
    move-object v2, v0

    .line 93
    :cond_7
    :goto_5
    invoke-virtual {v2, p1, p2}, Lcom/github/catvod/spider/merge/e/z;->a(Lcom/github/catvod/spider/merge/l/a;Ljava/lang/Object;)V

    .line 94
    .line 95
    .line 96
    return-void

    .line 97
    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_0
    .end packed-switch
.end method
