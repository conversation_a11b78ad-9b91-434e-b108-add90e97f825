.class public final Lcom/github/catvod/spider/merge/g/i;
.super Lcom/github/catvod/spider/merge/e/z;
.source "SourceFile"


# instance fields
.field public volatile a:Lcom/github/catvod/spider/merge/e/z;

.field public final synthetic b:Z

.field public final synthetic c:Lcom/github/catvod/spider/merge/e/n;

.field public final synthetic d:Lcom/github/catvod/spider/merge/k/a;

.field public final synthetic e:Lcom/github/catvod/spider/merge/g/j;


# direct methods
.method public constructor <init>(Lcom/github/catvod/spider/merge/g/j;ZLcom/github/catvod/spider/merge/e/n;Lcom/github/catvod/spider/merge/k/a;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lcom/github/catvod/spider/merge/g/i;->e:Lcom/github/catvod/spider/merge/g/j;

    .line 5
    .line 6
    iput-boolean p2, p0, Lcom/github/catvod/spider/merge/g/i;->b:Z

    .line 7
    .line 8
    iput-object p3, p0, Lcom/github/catvod/spider/merge/g/i;->c:Lcom/github/catvod/spider/merge/e/n;

    .line 9
    .line 10
    iput-object p4, p0, Lcom/github/catvod/spider/merge/g/i;->d:Lcom/github/catvod/spider/merge/k/a;

    .line 11
    .line 12
    return-void
.end method


# virtual methods
.method public final a(Lcom/github/catvod/spider/merge/l/a;Ljava/lang/Object;)V
    .locals 9

    .line 1
    iget-boolean v0, p0, Lcom/github/catvod/spider/merge/g/i;->b:Z

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {p1}, Lcom/github/catvod/spider/merge/l/a;->i()Lcom/github/catvod/spider/merge/l/a;

    .line 6
    .line 7
    .line 8
    return-void

    .line 9
    :cond_0
    iget-object v0, p0, Lcom/github/catvod/spider/merge/g/i;->a:Lcom/github/catvod/spider/merge/e/z;

    .line 10
    .line 11
    if-eqz v0, :cond_1

    .line 12
    .line 13
    goto/16 :goto_4

    .line 14
    .line 15
    :cond_1
    iget-object v0, p0, Lcom/github/catvod/spider/merge/g/i;->c:Lcom/github/catvod/spider/merge/e/n;

    .line 16
    .line 17
    iget-object v1, p0, Lcom/github/catvod/spider/merge/g/i;->e:Lcom/github/catvod/spider/merge/g/j;

    .line 18
    .line 19
    iget-object v2, p0, Lcom/github/catvod/spider/merge/g/i;->d:Lcom/github/catvod/spider/merge/k/a;

    .line 20
    .line 21
    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 22
    .line 23
    .line 24
    const-string v3, "skipPast must not be null"

    .line 25
    .line 26
    invoke-static {v1, v3}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    iget-object v3, v0, Lcom/github/catvod/spider/merge/e/n;->d:Lcom/github/catvod/spider/merge/h/j;

    .line 30
    .line 31
    invoke-virtual {v3}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 32
    .line 33
    .line 34
    sget-object v4, Lcom/github/catvod/spider/merge/h/j;->c:Lcom/github/catvod/spider/merge/h/i;

    .line 35
    .line 36
    if-ne v1, v4, :cond_2

    .line 37
    .line 38
    goto :goto_0

    .line 39
    :cond_2
    iget-object v4, v3, Lcom/github/catvod/spider/merge/h/j;->b:Ljava/util/concurrent/ConcurrentHashMap;

    .line 40
    .line 41
    iget-object v5, v2, Lcom/github/catvod/spider/merge/k/a;->a:Ljava/lang/Class;

    .line 42
    .line 43
    invoke-virtual {v4, v5}, Ljava/util/concurrent/ConcurrentHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 44
    .line 45
    .line 46
    move-result-object v6

    .line 47
    check-cast v6, Lcom/github/catvod/spider/merge/e/A;

    .line 48
    .line 49
    if-eqz v6, :cond_3

    .line 50
    .line 51
    if-ne v6, v1, :cond_7

    .line 52
    .line 53
    goto :goto_0

    .line 54
    :cond_3
    const-class v6, Lcom/github/catvod/spider/merge/f/a;

    .line 55
    .line 56
    invoke-virtual {v5, v6}, Ljava/lang/Class;->getAnnotation(Ljava/lang/Class;)Ljava/lang/annotation/Annotation;

    .line 57
    .line 58
    .line 59
    move-result-object v6

    .line 60
    check-cast v6, Lcom/github/catvod/spider/merge/f/a;

    .line 61
    .line 62
    if-nez v6, :cond_4

    .line 63
    .line 64
    goto :goto_1

    .line 65
    :cond_4
    invoke-interface {v6}, Lcom/github/catvod/spider/merge/f/a;->value()Ljava/lang/Class;

    .line 66
    .line 67
    .line 68
    move-result-object v6

    .line 69
    const-class v7, Lcom/github/catvod/spider/merge/e/A;

    .line 70
    .line 71
    invoke-virtual {v7, v6}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    .line 72
    .line 73
    .line 74
    move-result v7

    .line 75
    if-nez v7, :cond_5

    .line 76
    .line 77
    goto :goto_1

    .line 78
    :cond_5
    iget-object v7, v3, Lcom/github/catvod/spider/merge/h/j;->a:Lcom/github/catvod/spider/merge/g/h;

    .line 79
    .line 80
    new-instance v8, Lcom/github/catvod/spider/merge/k/a;

    .line 81
    .line 82
    invoke-direct {v8, v6}, Lcom/github/catvod/spider/merge/k/a;-><init>(Ljava/lang/reflect/Type;)V

    .line 83
    .line 84
    .line 85
    invoke-virtual {v7, v8}, Lcom/github/catvod/spider/merge/g/h;->b(Lcom/github/catvod/spider/merge/k/a;)Lcom/github/catvod/spider/merge/g/r;

    .line 86
    .line 87
    .line 88
    move-result-object v6

    .line 89
    invoke-interface {v6}, Lcom/github/catvod/spider/merge/g/r;->a()Ljava/lang/Object;

    .line 90
    .line 91
    .line 92
    move-result-object v6

    .line 93
    check-cast v6, Lcom/github/catvod/spider/merge/e/A;

    .line 94
    .line 95
    invoke-virtual {v4, v5, v6}, Ljava/util/concurrent/ConcurrentHashMap;->putIfAbsent(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 96
    .line 97
    .line 98
    move-result-object v4

    .line 99
    check-cast v4, Lcom/github/catvod/spider/merge/e/A;

    .line 100
    .line 101
    if-eqz v4, :cond_6

    .line 102
    .line 103
    move-object v6, v4

    .line 104
    :cond_6
    if-ne v6, v1, :cond_7

    .line 105
    .line 106
    :goto_0
    move-object v1, v3

    .line 107
    :cond_7
    :goto_1
    iget-object v3, v0, Lcom/github/catvod/spider/merge/e/n;->e:Ljava/util/List;

    .line 108
    .line 109
    invoke-interface {v3}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    .line 110
    .line 111
    .line 112
    move-result-object v3

    .line 113
    const/4 v4, 0x0

    .line 114
    :cond_8
    :goto_2
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    .line 115
    .line 116
    .line 117
    move-result v5

    .line 118
    if-eqz v5, :cond_a

    .line 119
    .line 120
    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 121
    .line 122
    .line 123
    move-result-object v5

    .line 124
    check-cast v5, Lcom/github/catvod/spider/merge/e/A;

    .line 125
    .line 126
    if-nez v4, :cond_9

    .line 127
    .line 128
    if-ne v5, v1, :cond_8

    .line 129
    .line 130
    const/4 v4, 0x1

    .line 131
    goto :goto_2

    .line 132
    :cond_9
    invoke-interface {v5, v0, v2}, Lcom/github/catvod/spider/merge/e/A;->a(Lcom/github/catvod/spider/merge/e/n;Lcom/github/catvod/spider/merge/k/a;)Lcom/github/catvod/spider/merge/e/z;

    .line 133
    .line 134
    .line 135
    move-result-object v5

    .line 136
    if-eqz v5, :cond_8

    .line 137
    .line 138
    move-object v0, v5

    .line 139
    goto :goto_3

    .line 140
    :cond_a
    if-nez v4, :cond_b

    .line 141
    .line 142
    invoke-virtual {v0, v2}, Lcom/github/catvod/spider/merge/e/n;->b(Lcom/github/catvod/spider/merge/k/a;)Lcom/github/catvod/spider/merge/e/z;

    .line 143
    .line 144
    .line 145
    move-result-object v0

    .line 146
    :goto_3
    iput-object v0, p0, Lcom/github/catvod/spider/merge/g/i;->a:Lcom/github/catvod/spider/merge/e/z;

    .line 147
    .line 148
    :goto_4
    invoke-virtual {v0, p1, p2}, Lcom/github/catvod/spider/merge/e/z;->a(Lcom/github/catvod/spider/merge/l/a;Ljava/lang/Object;)V

    .line 149
    .line 150
    .line 151
    return-void

    .line 152
    :cond_b
    new-instance p1, Ljava/lang/IllegalArgumentException;

    .line 153
    .line 154
    new-instance p2, Ljava/lang/StringBuilder;

    .line 155
    .line 156
    const-string v0, "GSON cannot serialize or deserialize "

    .line 157
    .line 158
    invoke-direct {p2, v0}, Ljava/lang/StringBuilder;-><init>(Ljava/lang/String;)V

    .line 159
    .line 160
    .line 161
    invoke-virtual {p2, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 162
    .line 163
    .line 164
    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 165
    .line 166
    .line 167
    move-result-object p2

    .line 168
    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 169
    .line 170
    .line 171
    throw p1
.end method
