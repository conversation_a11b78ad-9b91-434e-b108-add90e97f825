package com.github.catvod.spider;

import android.content.Context;
import android.util.Log;

import com.github.catvod.net.OkHttp;
import com.github.catvod.utils.DownloadUtils;
import com.github.catvod.utils.Notify;
import com.github.catvod.utils.Path;
import com.github.catvod.utils.Shell;
import com.github.catvod.utils.ScriptUtils;

import org.json.JSONObject;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;

import okhttp3.Response;

import com.flycc.vodproxy.sdk.Sdk;

public class VodProxy implements Runnable {

    private final Context context;

    public VodProxy(Context context) {
        this.context = context;
    }

    @Override
    public void run() {
        try {
            Sdk.startProxyServer(36150);
        } catch (Exception e) {
            Notify.show("启动视频代理失败");
        }
    }

}
