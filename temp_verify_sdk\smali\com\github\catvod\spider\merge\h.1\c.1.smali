.class public final Lcom/github/catvod/spider/merge/h/c;
.super Lcom/github/catvod/spider/merge/e/z;
.source "SourceFile"


# instance fields
.field public final synthetic a:I

.field public final b:Ljava/lang/Object;


# direct methods
.method public constructor <init>(Lcom/github/catvod/spider/merge/e/n;Lcom/github/catvod/spider/merge/e/z;Ljava/lang/reflect/Type;)V
    .locals 1

    const/4 v0, 0x0

    iput v0, p0, Lcom/github/catvod/spider/merge/h/c;->a:I

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    new-instance v0, Lcom/github/catvod/spider/merge/h/w;

    invoke-direct {v0, p1, p2, p3}, Lcom/github/catvod/spider/merge/h/w;-><init>(Lcom/github/catvod/spider/merge/e/n;Lcom/github/catvod/spider/merge/e/z;Ljava/lang/reflect/Type;)V

    iput-object v0, p0, Lcom/github/catvod/spider/merge/h/c;->b:Ljava/lang/Object;

    return-void
.end method

.method public constructor <init>(Lcom/github/catvod/spider/merge/h/U;)V
    .locals 1

    const/4 v0, 0x1

    iput v0, p0, Lcom/github/catvod/spider/merge/h/c;->a:I

    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    iput-object p1, p0, Lcom/github/catvod/spider/merge/h/c;->b:Ljava/lang/Object;

    return-void
.end method


# virtual methods
.method public final a(Lcom/github/catvod/spider/merge/l/a;Ljava/lang/Object;)V
    .locals 2

    .line 1
    iget v0, p0, Lcom/github/catvod/spider/merge/h/c;->a:I

    .line 2
    .line 3
    packed-switch v0, :pswitch_data_0

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Lcom/github/catvod/spider/merge/h/c;->b:Ljava/lang/Object;

    .line 7
    .line 8
    check-cast v0, Lcom/github/catvod/spider/merge/h/U;

    .line 9
    .line 10
    iget-object v0, v0, Lcom/github/catvod/spider/merge/h/U;->c:Lcom/github/catvod/spider/merge/e/z;

    .line 11
    .line 12
    invoke-virtual {v0, p1, p2}, Lcom/github/catvod/spider/merge/e/z;->a(Lcom/github/catvod/spider/merge/l/a;Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    return-void

    .line 16
    :pswitch_0
    check-cast p2, Ljava/util/Collection;

    .line 17
    .line 18
    if-nez p2, :cond_0

    .line 19
    .line 20
    invoke-virtual {p1}, Lcom/github/catvod/spider/merge/l/a;->i()Lcom/github/catvod/spider/merge/l/a;

    .line 21
    .line 22
    .line 23
    goto :goto_1

    .line 24
    :cond_0
    invoke-virtual {p1}, Lcom/github/catvod/spider/merge/l/a;->b()V

    .line 25
    .line 26
    .line 27
    invoke-interface {p2}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    .line 28
    .line 29
    .line 30
    move-result-object p2

    .line 31
    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 32
    .line 33
    .line 34
    move-result v0

    .line 35
    if-eqz v0, :cond_1

    .line 36
    .line 37
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    iget-object v1, p0, Lcom/github/catvod/spider/merge/h/c;->b:Ljava/lang/Object;

    .line 42
    .line 43
    check-cast v1, Lcom/github/catvod/spider/merge/h/w;

    .line 44
    .line 45
    invoke-virtual {v1, p1, v0}, Lcom/github/catvod/spider/merge/h/w;->a(Lcom/github/catvod/spider/merge/l/a;Ljava/lang/Object;)V

    .line 46
    .line 47
    .line 48
    goto :goto_0

    .line 49
    :cond_1
    invoke-virtual {p1}, Lcom/github/catvod/spider/merge/l/a;->e()V

    .line 50
    .line 51
    .line 52
    :goto_1
    return-void

    .line 53
    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_0
    .end packed-switch
.end method
