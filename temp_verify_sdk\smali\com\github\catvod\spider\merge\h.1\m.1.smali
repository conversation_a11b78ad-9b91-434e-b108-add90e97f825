.class public final Lcom/github/catvod/spider/merge/h/m;
.super Lcom/github/catvod/spider/merge/e/z;
.source "SourceFile"


# static fields
.field public static final b:Lcom/github/catvod/spider/merge/h/l;


# instance fields
.field public final synthetic a:I


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 1
    new-instance v0, Lcom/github/catvod/spider/merge/h/m;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, Lcom/github/catvod/spider/merge/h/m;-><init>(I)V

    .line 5
    .line 6
    .line 7
    new-instance v1, Lcom/github/catvod/spider/merge/h/l;

    .line 8
    .line 9
    const/4 v2, 0x0

    .line 10
    invoke-direct {v1, v0, v2}, Lcom/github/catvod/spider/merge/h/l;-><init>(Lcom/github/catvod/spider/merge/e/z;I)V

    .line 11
    .line 12
    .line 13
    sput-object v1, Lcom/github/catvod/spider/merge/h/m;->b:Lcom/github/catvod/spider/merge/h/l;

    .line 14
    .line 15
    return-void
.end method

.method public synthetic constructor <init>(I)V
    .locals 0

    .line 1
    iput p1, p0, Lcom/github/catvod/spider/merge/h/m;->a:I

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Lcom/github/catvod/spider/merge/l/a;Ljava/lang/Object;)V
    .locals 1

    .line 1
    iget v0, p0, Lcom/github/catvod/spider/merge/h/m;->a:I

    .line 2
    .line 3
    packed-switch v0, :pswitch_data_0

    .line 4
    .line 5
    .line 6
    invoke-virtual {p1}, Lcom/github/catvod/spider/merge/l/a;->i()Lcom/github/catvod/spider/merge/l/a;

    .line 7
    .line 8
    .line 9
    return-void

    .line 10
    :pswitch_0
    check-cast p2, Ljava/lang/Number;

    .line 11
    .line 12
    invoke-virtual {p1, p2}, Lcom/github/catvod/spider/merge/l/a;->p(Ljava/lang/Number;)V

    .line 13
    .line 14
    .line 15
    return-void

    .line 16
    nop

    .line 17
    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_0
    .end packed-switch
.end method

.method public toString()Ljava/lang/String;
    .locals 1

    .line 1
    iget v0, p0, Lcom/github/catvod/spider/merge/h/m;->a:I

    .line 2
    .line 3
    packed-switch v0, :pswitch_data_0

    .line 4
    .line 5
    .line 6
    invoke-super {p0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    return-object v0

    .line 11
    :pswitch_0
    const-string v0, "AnonymousOrNonStaticLocalClassAdapter"

    .line 12
    .line 13
    return-object v0

    .line 14
    nop

    .line 15
    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_0
    .end packed-switch
.end method
