.class public final Lcom/github/catvod/spider/merge/j/f;
.super Lcom/github/catvod/spider/merge/e/z;
.source "SourceFile"


# static fields
.field public static final b:Lcom/github/catvod/spider/merge/j/e;


# instance fields
.field public final a:Lcom/github/catvod/spider/merge/e/z;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lcom/github/catvod/spider/merge/j/e;

    .line 2
    .line 3
    invoke-direct {v0}, Lcom/github/catvod/spider/merge/j/e;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, Lcom/github/catvod/spider/merge/j/f;->b:Lcom/github/catvod/spider/merge/j/e;

    .line 7
    .line 8
    return-void
.end method

.method public constructor <init>(Lcom/github/catvod/spider/merge/e/z;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lcom/github/catvod/spider/merge/j/f;->a:Lcom/github/catvod/spider/merge/e/z;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final a(Lcom/github/catvod/spider/merge/l/a;Ljava/lang/Object;)V
    .locals 1

    .line 1
    check-cast p2, Ljava/sql/Timestamp;

    .line 2
    .line 3
    iget-object v0, p0, Lcom/github/catvod/spider/merge/j/f;->a:Lcom/github/catvod/spider/merge/e/z;

    .line 4
    .line 5
    invoke-virtual {v0, p1, p2}, Lcom/github/catvod/spider/merge/e/z;->a(Lcom/github/catvod/spider/merge/l/a;Ljava/lang/Object;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method
