.class public Lcom/github/catvod/spider/merge/a/g;
.super Ljava/lang/Object;
.source "SourceFile"


# instance fields
.field private a:Ljava/lang/String;
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "type"
    .end annotation
.end field

.field private b:Ljava/lang/Float;
    .annotation runtime Lcom/github/catvod/spider/merge/f/b;
        value = "ratio"
    .end annotation
.end field
