.class public final Lcom/github/catvod/spider/merge/h/o;
.super Lcom/github/catvod/spider/merge/e/z;
.source "SourceFile"


# static fields
.field public static final b:Lcom/github/catvod/spider/merge/h/n;


# instance fields
.field public final a:Lcom/github/catvod/spider/merge/e/n;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    sget-object v0, Lcom/github/catvod/spider/merge/e/y;->a:Lcom/github/catvod/spider/merge/e/u;

    .line 2
    .line 3
    new-instance v1, Lcom/github/catvod/spider/merge/h/n;

    .line 4
    .line 5
    invoke-direct {v1, v0}, Lcom/github/catvod/spider/merge/h/n;-><init>(Lcom/github/catvod/spider/merge/e/y;)V

    .line 6
    .line 7
    .line 8
    sput-object v1, Lcom/github/catvod/spider/merge/h/o;->b:Lcom/github/catvod/spider/merge/h/n;

    .line 9
    .line 10
    return-void
.end method

.method public constructor <init>(Lcom/github/catvod/spider/merge/e/n;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lcom/github/catvod/spider/merge/h/o;->a:Lcom/github/catvod/spider/merge/e/n;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final a(Lcom/github/catvod/spider/merge/l/a;Ljava/lang/Object;)V
    .locals 3

    .line 1
    if-nez p2, :cond_0

    .line 2
    .line 3
    invoke-virtual {p1}, Lcom/github/catvod/spider/merge/l/a;->i()Lcom/github/catvod/spider/merge/l/a;

    .line 4
    .line 5
    .line 6
    return-void

    .line 7
    :cond_0
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    iget-object v1, p0, Lcom/github/catvod/spider/merge/h/o;->a:Lcom/github/catvod/spider/merge/e/n;

    .line 12
    .line 13
    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 14
    .line 15
    .line 16
    new-instance v2, Lcom/github/catvod/spider/merge/k/a;

    .line 17
    .line 18
    invoke-direct {v2, v0}, Lcom/github/catvod/spider/merge/k/a;-><init>(Ljava/lang/reflect/Type;)V

    .line 19
    .line 20
    .line 21
    invoke-virtual {v1, v2}, Lcom/github/catvod/spider/merge/e/n;->b(Lcom/github/catvod/spider/merge/k/a;)Lcom/github/catvod/spider/merge/e/z;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    instance-of v1, v0, Lcom/github/catvod/spider/merge/h/o;

    .line 26
    .line 27
    if-eqz v1, :cond_1

    .line 28
    .line 29
    invoke-virtual {p1}, Lcom/github/catvod/spider/merge/l/a;->c()V

    .line 30
    .line 31
    .line 32
    invoke-virtual {p1}, Lcom/github/catvod/spider/merge/l/a;->f()V

    .line 33
    .line 34
    .line 35
    return-void

    .line 36
    :cond_1
    invoke-virtual {v0, p1, p2}, Lcom/github/catvod/spider/merge/e/z;->a(Lcom/github/catvod/spider/merge/l/a;Ljava/lang/Object;)V

    .line 37
    .line 38
    .line 39
    return-void
.end method
