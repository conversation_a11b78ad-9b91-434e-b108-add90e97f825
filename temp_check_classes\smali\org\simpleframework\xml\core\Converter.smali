.class interface abstract Lorg/simpleframework/xml/core/Converter;
.super Ljava/lang/Object;
.source "SourceFile"


# virtual methods
.method public abstract read(Lorg/simpleframework/xml/stream/InputNode;)Ljava/lang/Object;
.end method

.method public abstract read(Lorg/simpleframework/xml/stream/InputNode;Ljava/lang/Object;)Ljava/lang/Object;
.end method

.method public abstract validate(Lorg/simpleframework/xml/stream/InputNode;)Z
.end method

.method public abstract write(Lorg/simpleframework/xml/stream/OutputNode;Ljava/lang/Object;)V
.end method
