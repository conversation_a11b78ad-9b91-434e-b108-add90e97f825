.class public Lcom/github/catvod/spider/VodProxy;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public run()V
    .locals 2

    .line 1
    const-wide/32 v0, 0x8d36

    .line 2
    .line 3
    .line 4
    :try_start_0
    invoke-static {v0, v1}, Lcom/flycc/vodproxy/sdk/Sdk;->startProxyServer(J)J
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 5
    .line 6
    .line 7
    return-void

    .line 8
    :catch_0
    new-instance v0, Lcom/github/catvod/spider/merge/d/b;

    .line 9
    .line 10
    const-string v1, "\u542f\u52a8\u89c6\u9891\u4ee3\u7406\u5931\u8d25"

    .line 11
    .line 12
    invoke-direct {v0, v1}, Lcom/github/catvod/spider/merge/d/b;-><init>(Ljava/lang/String;)V

    .line 13
    .line 14
    .line 15
    invoke-static {v0}, Lcom/github/catvod/spider/Init;->run(Ljava/lang/Runnable;)V

    .line 16
    .line 17
    .line 18
    return-void
.end method
