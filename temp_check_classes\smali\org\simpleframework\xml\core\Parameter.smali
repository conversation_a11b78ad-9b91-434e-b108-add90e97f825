.class interface abstract Lorg/simpleframework/xml/core/Parameter;
.super Ljava/lang/Object;
.source "SourceFile"


# virtual methods
.method public abstract getAnnotation()Ljava/lang/annotation/Annotation;
.end method

.method public abstract getExpression()Lorg/simpleframework/xml/core/Expression;
.end method

.method public abstract getIndex()I
.end method

.method public abstract getKey()Ljava/lang/Object;
.end method

.method public abstract getName()Ljava/lang/String;
.end method

.method public abstract getPath()Ljava/lang/String;
.end method

.method public abstract getType()Ljava/lang/Class;
.end method

.method public abstract isAttribute()Z
.end method

.method public abstract isPrimitive()Z
.end method

.method public abstract isRequired()Z
.end method

.method public abstract isText()Z
.end method

.method public abstract toString()Ljava/lang/String;
.end method
