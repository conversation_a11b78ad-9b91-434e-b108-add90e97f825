.class final Lokhttp3/internal/ws/RealWebSocket$failWebSocket$1$1;
.super Lcom/github/catvod/spider/merge/C/g;
.source "SourceFile"

# interfaces
.implements Lcom/github/catvod/spider/merge/B/a;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/github/catvod/spider/merge/C/g;",
        "Lcom/github/catvod/spider/merge/B/a;"
    }
.end annotation


# instance fields
.field final synthetic $streamsToClose:Lcom/github/catvod/spider/merge/C/j;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/github/catvod/spider/merge/C/j;"
        }
    .end annotation
.end field

.field final synthetic $writerToClose:Lcom/github/catvod/spider/merge/C/j;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/github/catvod/spider/merge/C/j;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lcom/github/catvod/spider/merge/C/j;Lcom/github/catvod/spider/merge/C/j;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/github/catvod/spider/merge/C/j;",
            "Lcom/github/catvod/spider/merge/C/j;",
            ")V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lokhttp3/internal/ws/RealWebSocket$failWebSocket$1$1;->$writerToClose:Lcom/github/catvod/spider/merge/C/j;

    .line 2
    .line 3
    iput-object p2, p0, Lokhttp3/internal/ws/RealWebSocket$failWebSocket$1$1;->$streamsToClose:Lcom/github/catvod/spider/merge/C/j;

    .line 4
    .line 5
    const/4 p1, 0x0

    .line 6
    invoke-direct {p0, p1}, Lcom/github/catvod/spider/merge/C/g;-><init>(I)V

    .line 7
    .line 8
    .line 9
    return-void
.end method


# virtual methods
.method public bridge synthetic invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lokhttp3/internal/ws/RealWebSocket$failWebSocket$1$1;->invoke()V

    sget-object v0, Lcom/github/catvod/spider/merge/p/f;->c:Lcom/github/catvod/spider/merge/p/f;

    return-object v0
.end method

.method public final invoke()V
    .locals 1

    .line 2
    iget-object v0, p0, Lokhttp3/internal/ws/RealWebSocket$failWebSocket$1$1;->$writerToClose:Lcom/github/catvod/spider/merge/C/j;

    iget-object v0, v0, Lcom/github/catvod/spider/merge/C/j;->a:Ljava/lang/Object;

    check-cast v0, Ljava/io/Closeable;

    invoke-static {v0}, Lokhttp3/internal/_UtilCommonKt;->closeQuietly(Ljava/io/Closeable;)V

    .line 3
    iget-object v0, p0, Lokhttp3/internal/ws/RealWebSocket$failWebSocket$1$1;->$streamsToClose:Lcom/github/catvod/spider/merge/C/j;

    iget-object v0, v0, Lcom/github/catvod/spider/merge/C/j;->a:Ljava/lang/Object;

    check-cast v0, Lokhttp3/internal/ws/RealWebSocket$Streams;

    if-eqz v0, :cond_0

    invoke-static {v0}, Lokhttp3/internal/_UtilCommonKt;->closeQuietly(Ljava/io/Closeable;)V

    :cond_0
    return-void
.end method
