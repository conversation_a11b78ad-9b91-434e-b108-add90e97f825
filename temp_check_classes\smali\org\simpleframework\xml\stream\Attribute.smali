.class interface abstract Lorg/simpleframework/xml/stream/Attribute;
.super Ljava/lang/Object;
.source "SourceFile"


# virtual methods
.method public abstract getName()Ljava/lang/String;
.end method

.method public abstract getPrefix()Ljava/lang/String;
.end method

.method public abstract getReference()Ljava/lang/String;
.end method

.method public abstract getSource()Ljava/lang/Object;
.end method

.method public abstract getValue()Ljava/lang/String;
.end method

.method public abstract isReserved()Z
.end method
