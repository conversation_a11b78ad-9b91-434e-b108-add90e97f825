.class public abstract enum Lcom/github/catvod/spider/merge/e/y;
.super Ljava/lang/Enum;
.source "SourceFile"


# static fields
.field public static final enum a:Lcom/github/catvod/spider/merge/e/u;

.field public static final enum b:Lcom/github/catvod/spider/merge/e/v;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lcom/github/catvod/spider/merge/e/u;

    .line 2
    .line 3
    invoke-direct {v0}, Lcom/github/catvod/spider/merge/e/u;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, Lcom/github/catvod/spider/merge/e/y;->a:Lcom/github/catvod/spider/merge/e/u;

    .line 7
    .line 8
    new-instance v0, Lcom/github/catvod/spider/merge/e/v;

    .line 9
    .line 10
    invoke-direct {v0}, Lcom/github/catvod/spider/merge/e/v;-><init>()V

    .line 11
    .line 12
    .line 13
    sput-object v0, Lcom/github/catvod/spider/merge/e/y;->b:Lcom/github/catvod/spider/merge/e/v;

    .line 14
    .line 15
    return-void
.end method
