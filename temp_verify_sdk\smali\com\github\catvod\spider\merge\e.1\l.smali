.class public final Lcom/github/catvod/spider/merge/e/l;
.super Lcom/github/catvod/spider/merge/e/z;
.source "SourceFile"


# instance fields
.field public final synthetic a:I

.field public final synthetic b:Lcom/github/catvod/spider/merge/e/z;


# direct methods
.method public synthetic constructor <init>(Lcom/github/catvod/spider/merge/e/z;I)V
    .locals 0

    .line 1
    iput p2, p0, Lcom/github/catvod/spider/merge/e/l;->a:I

    iput-object p1, p0, Lcom/github/catvod/spider/merge/e/l;->b:Lcom/github/catvod/spider/merge/e/z;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Lcom/github/catvod/spider/merge/l/a;Ljava/lang/Object;)V
    .locals 4

    .line 1
    iget v0, p0, Lcom/github/catvod/spider/merge/e/l;->a:I

    .line 2
    .line 3
    packed-switch v0, :pswitch_data_0

    .line 4
    .line 5
    .line 6
    if-nez p2, :cond_0

    .line 7
    .line 8
    invoke-virtual {p1}, Lcom/github/catvod/spider/merge/l/a;->i()Lcom/github/catvod/spider/merge/l/a;

    .line 9
    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    iget-object v0, p0, Lcom/github/catvod/spider/merge/e/l;->b:Lcom/github/catvod/spider/merge/e/z;

    .line 13
    .line 14
    invoke-virtual {v0, p1, p2}, Lcom/github/catvod/spider/merge/e/z;->a(Lcom/github/catvod/spider/merge/l/a;Ljava/lang/Object;)V

    .line 15
    .line 16
    .line 17
    :goto_0
    return-void

    .line 18
    :pswitch_0
    check-cast p2, Ljava/util/concurrent/atomic/AtomicLongArray;

    .line 19
    .line 20
    invoke-virtual {p1}, Lcom/github/catvod/spider/merge/l/a;->b()V

    .line 21
    .line 22
    .line 23
    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLongArray;->length()I

    .line 24
    .line 25
    .line 26
    move-result v0

    .line 27
    const/4 v1, 0x0

    .line 28
    :goto_1
    if-ge v1, v0, :cond_1

    .line 29
    .line 30
    invoke-virtual {p2, v1}, Ljava/util/concurrent/atomic/AtomicLongArray;->get(I)J

    .line 31
    .line 32
    .line 33
    move-result-wide v2

    .line 34
    invoke-static {v2, v3}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 35
    .line 36
    .line 37
    move-result-object v2

    .line 38
    iget-object v3, p0, Lcom/github/catvod/spider/merge/e/l;->b:Lcom/github/catvod/spider/merge/e/z;

    .line 39
    .line 40
    invoke-virtual {v3, p1, v2}, Lcom/github/catvod/spider/merge/e/z;->a(Lcom/github/catvod/spider/merge/l/a;Ljava/lang/Object;)V

    .line 41
    .line 42
    .line 43
    add-int/lit8 v1, v1, 0x1

    .line 44
    .line 45
    goto :goto_1

    .line 46
    :cond_1
    invoke-virtual {p1}, Lcom/github/catvod/spider/merge/l/a;->e()V

    .line 47
    .line 48
    .line 49
    return-void

    .line 50
    :pswitch_1
    check-cast p2, Ljava/util/concurrent/atomic/AtomicLong;

    .line 51
    .line 52
    invoke-virtual {p2}, Ljava/util/concurrent/atomic/AtomicLong;->get()J

    .line 53
    .line 54
    .line 55
    move-result-wide v0

    .line 56
    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    .line 57
    .line 58
    .line 59
    move-result-object p2

    .line 60
    iget-object v0, p0, Lcom/github/catvod/spider/merge/e/l;->b:Lcom/github/catvod/spider/merge/e/z;

    .line 61
    .line 62
    invoke-virtual {v0, p1, p2}, Lcom/github/catvod/spider/merge/e/z;->a(Lcom/github/catvod/spider/merge/l/a;Ljava/lang/Object;)V

    .line 63
    .line 64
    .line 65
    return-void

    .line 66
    nop

    .line 67
    :pswitch_data_0
    .packed-switch 0x0
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method
