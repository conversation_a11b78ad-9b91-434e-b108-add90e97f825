.class Lcom/github/catvod/spider/merge/a/a;
.super Lcom/github/catvod/spider/merge/k/a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/github/catvod/spider/merge/k/a<",
        "Ljava/util/List<",
        "Lcom/github/catvod/spider/merge/a/b;",
        ">;>;"
    }
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Lcom/github/catvod/spider/merge/k/a;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
