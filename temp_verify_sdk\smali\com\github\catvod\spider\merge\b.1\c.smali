.class public abstract Lcom/github/catvod/spider/merge/b/c;
.super Ljava/lang/Object;
.source "SourceFile"


# static fields
.field public static volatile a:Lcom/github/catvod/spider/merge/G/e;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, Lcom/github/catvod/spider/merge/G/e;

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    invoke-direct {v0, v1}, Lcom/github/catvod/spider/merge/G/e;-><init>(I)V

    .line 5
    .line 6
    .line 7
    sput-object v0, Lcom/github/catvod/spider/merge/b/c;->a:Lcom/github/catvod/spider/merge/G/e;

    .line 8
    .line 9
    return-void
.end method
