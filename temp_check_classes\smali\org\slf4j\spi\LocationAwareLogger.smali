.class public interface abstract Lorg/slf4j/spi/LocationAwareLogger;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lorg/slf4j/Logger;


# static fields
.field public static final DEBUG_INT:I = 0xa

.field public static final ERROR_INT:I = 0x28

.field public static final INFO_INT:I = 0x14

.field public static final TRACE_INT:I = 0x0

.field public static final WARN_INT:I = 0x1e


# virtual methods
.method public abstract log(Lorg/slf4j/Marker;Ljava/lang/String;ILjava/lang/String;[Ljava/lang/Object;Ljava/lang/Throwable;)V
.end method
