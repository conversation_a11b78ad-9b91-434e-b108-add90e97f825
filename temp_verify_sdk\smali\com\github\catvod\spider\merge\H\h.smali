.class public final Lcom/github/catvod/spider/merge/H/h;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lcom/github/catvod/spider/merge/H/d;


# instance fields
.field public final a:Ljava/lang/Object;

.field public final b:Ljava/lang/Object;

.field public c:Ljava/lang/Object;


# direct methods
.method public constructor <init>(Ljava/lang/reflect/Method;Ljava/lang/reflect/Method;Ljava/lang/reflect/Method;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    iput-object p1, p0, Lcom/github/catvod/spider/merge/H/h;->a:Ljava/lang/Object;

    .line 3
    iput-object p2, p0, Lcom/github/catvod/spider/merge/H/h;->b:Ljava/lang/Object;

    .line 4
    iput-object p3, p0, Lcom/github/catvod/spider/merge/H/h;->c:Ljava/lang/Object;

    return-void
.end method

.method public constructor <init>(Ljava/util/regex/Matcher;Ljava/lang/CharSequence;)V
    .locals 0

    .line 5
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/github/catvod/spider/merge/H/h;->a:Ljava/lang/Object;

    .line 6
    new-instance p1, Lcom/github/catvod/spider/merge/H/g;

    invoke-direct {p1, p0}, Lcom/github/catvod/spider/merge/H/g;-><init>(Lcom/github/catvod/spider/merge/H/h;)V

    iput-object p1, p0, Lcom/github/catvod/spider/merge/H/h;->b:Ljava/lang/Object;

    return-void
.end method
