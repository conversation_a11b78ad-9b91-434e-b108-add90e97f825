.class final Lokio/internal/ZipFilesKt$readCentralDirectoryZipEntry$1;
.super Lcom/github/catvod/spider/merge/C/g;
.source "SourceFile"

# interfaces
.implements Lcom/github/catvod/spider/merge/B/p;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/github/catvod/spider/merge/C/g;",
        "Lcom/github/catvod/spider/merge/B/p;"
    }
.end annotation


# instance fields
.field final synthetic $compressedSize:Lcom/github/catvod/spider/merge/C/i;

.field final synthetic $hasZip64Extra:Lcom/github/catvod/spider/merge/C/h;

.field final synthetic $ntfsCreatedAtFiletime:Lcom/github/catvod/spider/merge/C/j;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/github/catvod/spider/merge/C/j;"
        }
    .end annotation
.end field

.field final synthetic $ntfsLastAccessedAtFiletime:Lcom/github/catvod/spider/merge/C/j;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/github/catvod/spider/merge/C/j;"
        }
    .end annotation
.end field

.field final synthetic $ntfsLastModifiedAtFiletime:Lcom/github/catvod/spider/merge/C/j;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lcom/github/catvod/spider/merge/C/j;"
        }
    .end annotation
.end field

.field final synthetic $offset:Lcom/github/catvod/spider/merge/C/i;

.field final synthetic $requiredZip64ExtraSize:J

.field final synthetic $size:Lcom/github/catvod/spider/merge/C/i;

.field final synthetic $this_readCentralDirectoryZipEntry:Lokio/BufferedSource;


# direct methods
.method public constructor <init>(Lcom/github/catvod/spider/merge/C/h;JLcom/github/catvod/spider/merge/C/i;Lokio/BufferedSource;Lcom/github/catvod/spider/merge/C/i;Lcom/github/catvod/spider/merge/C/i;Lcom/github/catvod/spider/merge/C/j;Lcom/github/catvod/spider/merge/C/j;Lcom/github/catvod/spider/merge/C/j;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/github/catvod/spider/merge/C/h;",
            "J",
            "Lcom/github/catvod/spider/merge/C/i;",
            "Lokio/BufferedSource;",
            "Lcom/github/catvod/spider/merge/C/i;",
            "Lcom/github/catvod/spider/merge/C/i;",
            "Lcom/github/catvod/spider/merge/C/j;",
            "Lcom/github/catvod/spider/merge/C/j;",
            "Lcom/github/catvod/spider/merge/C/j;",
            ")V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lokio/internal/ZipFilesKt$readCentralDirectoryZipEntry$1;->$hasZip64Extra:Lcom/github/catvod/spider/merge/C/h;

    iput-wide p2, p0, Lokio/internal/ZipFilesKt$readCentralDirectoryZipEntry$1;->$requiredZip64ExtraSize:J

    iput-object p4, p0, Lokio/internal/ZipFilesKt$readCentralDirectoryZipEntry$1;->$size:Lcom/github/catvod/spider/merge/C/i;

    iput-object p5, p0, Lokio/internal/ZipFilesKt$readCentralDirectoryZipEntry$1;->$this_readCentralDirectoryZipEntry:Lokio/BufferedSource;

    iput-object p6, p0, Lokio/internal/ZipFilesKt$readCentralDirectoryZipEntry$1;->$compressedSize:Lcom/github/catvod/spider/merge/C/i;

    iput-object p7, p0, Lokio/internal/ZipFilesKt$readCentralDirectoryZipEntry$1;->$offset:Lcom/github/catvod/spider/merge/C/i;

    iput-object p8, p0, Lokio/internal/ZipFilesKt$readCentralDirectoryZipEntry$1;->$ntfsLastModifiedAtFiletime:Lcom/github/catvod/spider/merge/C/j;

    iput-object p9, p0, Lokio/internal/ZipFilesKt$readCentralDirectoryZipEntry$1;->$ntfsLastAccessedAtFiletime:Lcom/github/catvod/spider/merge/C/j;

    iput-object p10, p0, Lokio/internal/ZipFilesKt$readCentralDirectoryZipEntry$1;->$ntfsCreatedAtFiletime:Lcom/github/catvod/spider/merge/C/j;

    const/4 p1, 0x2

    invoke-direct {p0, p1}, Lcom/github/catvod/spider/merge/C/g;-><init>(I)V

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 2

    .line 1
    check-cast p1, Ljava/lang/Number;

    invoke-virtual {p1}, Ljava/lang/Number;->intValue()I

    move-result p1

    check-cast p2, Ljava/lang/Number;

    invoke-virtual {p2}, Ljava/lang/Number;->longValue()J

    move-result-wide v0

    invoke-virtual {p0, p1, v0, v1}, Lokio/internal/ZipFilesKt$readCentralDirectoryZipEntry$1;->invoke(IJ)V

    sget-object p1, Lcom/github/catvod/spider/merge/p/f;->c:Lcom/github/catvod/spider/merge/p/f;

    return-object p1
.end method

.method public final invoke(IJ)V
    .locals 5

    const/4 v0, 0x1

    if-eq p1, v0, :cond_2

    const/16 v0, 0xa

    if-eq p1, v0, :cond_0

    return-void

    :cond_0
    const-wide/16 v0, 0x4

    cmp-long p1, p2, v0

    if-ltz p1, :cond_1

    .line 2
    iget-object p1, p0, Lokio/internal/ZipFilesKt$readCentralDirectoryZipEntry$1;->$this_readCentralDirectoryZipEntry:Lokio/BufferedSource;

    invoke-interface {p1, v0, v1}, Lokio/BufferedSource;->skip(J)V

    .line 3
    iget-object p1, p0, Lokio/internal/ZipFilesKt$readCentralDirectoryZipEntry$1;->$this_readCentralDirectoryZipEntry:Lokio/BufferedSource;

    sub-long/2addr p2, v0

    long-to-int p3, p2

    new-instance p2, Lokio/internal/ZipFilesKt$readCentralDirectoryZipEntry$1$1;

    iget-object v0, p0, Lokio/internal/ZipFilesKt$readCentralDirectoryZipEntry$1;->$ntfsLastModifiedAtFiletime:Lcom/github/catvod/spider/merge/C/j;

    iget-object v1, p0, Lokio/internal/ZipFilesKt$readCentralDirectoryZipEntry$1;->$ntfsLastAccessedAtFiletime:Lcom/github/catvod/spider/merge/C/j;

    iget-object v2, p0, Lokio/internal/ZipFilesKt$readCentralDirectoryZipEntry$1;->$ntfsCreatedAtFiletime:Lcom/github/catvod/spider/merge/C/j;

    invoke-direct {p2, v0, p1, v1, v2}, Lokio/internal/ZipFilesKt$readCentralDirectoryZipEntry$1$1;-><init>(Lcom/github/catvod/spider/merge/C/j;Lokio/BufferedSource;Lcom/github/catvod/spider/merge/C/j;Lcom/github/catvod/spider/merge/C/j;)V

    invoke-static {p1, p3, p2}, Lokio/internal/ZipFilesKt;->access$readExtra(Lokio/BufferedSource;ILcom/github/catvod/spider/merge/B/p;)V

    return-void

    .line 4
    :cond_1
    new-instance p1, Ljava/io/IOException;

    const-string p2, "bad zip: NTFS extra too short"

    invoke-direct {p1, p2}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 5
    :cond_2
    iget-object p1, p0, Lokio/internal/ZipFilesKt$readCentralDirectoryZipEntry$1;->$hasZip64Extra:Lcom/github/catvod/spider/merge/C/h;

    iget-boolean v1, p1, Lcom/github/catvod/spider/merge/C/h;->a:Z

    if-nez v1, :cond_7

    .line 6
    iput-boolean v0, p1, Lcom/github/catvod/spider/merge/C/h;->a:Z

    .line 7
    iget-wide v0, p0, Lokio/internal/ZipFilesKt$readCentralDirectoryZipEntry$1;->$requiredZip64ExtraSize:J

    cmp-long p1, p2, v0

    if-ltz p1, :cond_6

    .line 8
    iget-object p1, p0, Lokio/internal/ZipFilesKt$readCentralDirectoryZipEntry$1;->$size:Lcom/github/catvod/spider/merge/C/i;

    iget-wide p2, p1, Lcom/github/catvod/spider/merge/C/i;->a:J

    const-wide v0, 0xffffffffL

    cmp-long v2, p2, v0

    if-nez v2, :cond_3

    iget-object p2, p0, Lokio/internal/ZipFilesKt$readCentralDirectoryZipEntry$1;->$this_readCentralDirectoryZipEntry:Lokio/BufferedSource;

    invoke-interface {p2}, Lokio/BufferedSource;->readLongLe()J

    move-result-wide p2

    :cond_3
    iput-wide p2, p1, Lcom/github/catvod/spider/merge/C/i;->a:J

    .line 9
    iget-object p1, p0, Lokio/internal/ZipFilesKt$readCentralDirectoryZipEntry$1;->$compressedSize:Lcom/github/catvod/spider/merge/C/i;

    iget-wide p2, p1, Lcom/github/catvod/spider/merge/C/i;->a:J

    const-wide/16 v2, 0x0

    cmp-long v4, p2, v0

    if-nez v4, :cond_4

    iget-object p2, p0, Lokio/internal/ZipFilesKt$readCentralDirectoryZipEntry$1;->$this_readCentralDirectoryZipEntry:Lokio/BufferedSource;

    invoke-interface {p2}, Lokio/BufferedSource;->readLongLe()J

    move-result-wide p2

    goto :goto_0

    :cond_4
    move-wide p2, v2

    :goto_0
    iput-wide p2, p1, Lcom/github/catvod/spider/merge/C/i;->a:J

    .line 10
    iget-object p1, p0, Lokio/internal/ZipFilesKt$readCentralDirectoryZipEntry$1;->$offset:Lcom/github/catvod/spider/merge/C/i;

    iget-wide p2, p1, Lcom/github/catvod/spider/merge/C/i;->a:J

    cmp-long v4, p2, v0

    if-nez v4, :cond_5

    iget-object p2, p0, Lokio/internal/ZipFilesKt$readCentralDirectoryZipEntry$1;->$this_readCentralDirectoryZipEntry:Lokio/BufferedSource;

    invoke-interface {p2}, Lokio/BufferedSource;->readLongLe()J

    move-result-wide v2

    :cond_5
    iput-wide v2, p1, Lcom/github/catvod/spider/merge/C/i;->a:J

    return-void

    .line 11
    :cond_6
    new-instance p1, Ljava/io/IOException;

    const-string p2, "bad zip: zip64 extra too short"

    invoke-direct {p1, p2}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    throw p1

    .line 12
    :cond_7
    new-instance p1, Ljava/io/IOException;

    const-string p2, "bad zip: zip64 extra repeated"

    invoke-direct {p1, p2}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    throw p1
.end method
