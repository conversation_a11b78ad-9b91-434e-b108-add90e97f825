.class public interface abstract Lorg/simpleframework/xml/strategy/Value;
.super Ljava/lang/Object;
.source "SourceFile"


# virtual methods
.method public abstract getLength()I
.end method

.method public abstract getType()Ljava/lang/Class;
.end method

.method public abstract getValue()Ljava/lang/Object;
.end method

.method public abstract isReference()Z
.end method

.method public abstract setValue(Ljava/lang/Object;)V
.end method
