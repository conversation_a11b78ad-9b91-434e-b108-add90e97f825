# .so文件打包和加载说明

## 概述

本项目已经修改了`genJar.bat`脚本，现在可以自动将APK中的.so文件打包到生成的jar中，并提供了在jar中加载这些.so文件的机制。

## 修改内容

### 1. 脚本修改 (genJar.bat)

- 添加了自动复制.so文件的功能
- 添加了自动复制assets文件的功能
- 支持所有架构：arm64-v8a, armeabi-v7a, x86, x86_64
- 自动检测APK中的lib目录和assets目录并复制到jar中

### 2. 新增工具类 (NativeLibraryLoader.java)

创建了`com.github.catvod.utils.NativeLibraryLoader`类，用于：
- 从jar中提取.so文件到临时目录
- 根据设备架构自动选择合适的.so文件
- 提供统一的库加载接口

### 3. 初始化修改 (Init.java)

在`Init.init()`方法中添加了native库的自动加载：
```java
// 加载native库
try {
    com.github.catvod.utils.NativeLibraryLoader.loadGojniLibrary(context);
    SpiderDebug.log("Native库加载成功");
} catch (Exception e) {
    SpiderDebug.log("Native库加载失败: " + e.getMessage());
}
```

## 工作原理

### 1. 打包过程

1. `genJar.bat`使用apktool解包APK
2. 检测APK中的`lib/`目录和`assets/`目录
3. 将所有架构的.so文件复制到`spider.jar/lib/`目录
4. 将所有assets文件复制到`spider.jar/assets/`目录
5. 使用apktool重新打包成jar文件

### 2. 加载过程

1. 应用启动时调用`Init.init()`
2. `NativeLibraryLoader.loadGojniLibrary()`被调用
3. 首先尝试使用`System.loadLibrary("gojni")`从系统加载
4. 如果系统加载失败，则：
   - 检测设备架构（arm64-v8a, armeabi-v7a等）
   - 从jar中提取对应架构的`libgojni.so`到临时目录
   - 使用`System.load()`加载提取的.so文件

## 支持的架构

- **arm64-v8a**: 64位ARM架构（现代Android设备）
- **armeabi-v7a**: 32位ARM架构（较老的Android设备）
- **x86**: 32位x86架构（模拟器）
- **x86_64**: 64位x86架构（模拟器）

## 使用方法

### 在代码中引用.so文件

由于库加载已经在`Init.init()`中自动处理，你只需要：

1. 确保在使用native方法前调用了`Init.init(context)`
2. 直接使用native方法，例如：
   ```java
   // VodProxy.java中的使用示例
   Sdk.startProxyServer(36150);
   ```

### 检查库是否已加载

```java
if (NativeLibraryLoader.isLibraryLoaded()) {
    // 库已加载，可以安全使用native方法
    Sdk.startProxyServer(36150);
} else {
    // 库未加载，处理错误情况
    Log.e(TAG, "Native库未加载");
}
```

## 注意事项

1. **架构兼容性**: 确保jar中包含目标设备架构的.so文件
2. **权限要求**: 应用需要有写入缓存目录的权限（通常默认具有）
3. **文件大小**: 包含多个架构的.so文件会增加jar文件大小
4. **首次加载**: 第一次加载时需要提取.so文件，可能稍慢

## 故障排除

### 常见问题

1. **UnsatisfiedLinkError**: 
   - 检查jar中是否包含对应架构的.so文件
   - 确保`Init.init()`在使用native方法前被调用

2. **文件提取失败**:
   - 检查应用是否有缓存目录写入权限
   - 检查设备存储空间是否充足

3. **架构不匹配**:
   - 确认设备架构与jar中的.so文件架构匹配
   - 可以通过`Build.SUPPORTED_ABIS`查看设备支持的架构

### 调试信息

库加载过程会输出详细的日志信息，可以通过以下方式查看：
```java
SpiderDebug.log("查看库加载状态");
```

## 文件结构

生成的jar文件结构：
```
custom_spider.jar
├── AndroidManifest.xml
├── classes.dex
├── lib/
│   ├── arm64-v8a/
│   │   ├── libgojni.so
│   │   └── libquickjs-android-wrapper.so
│   ├── armeabi-v7a/
│   │   ├── libgojni.so
│   │   └── libquickjs-android-wrapper.so
│   ├── x86/
│   │   ├── libgojni.so
│   │   └── libquickjs-android-wrapper.so
│   └── x86_64/
│       ├── libgojni.so
│       └── libquickjs-android-wrapper.so
└── assets/
    ├── spider_server_config.json
    ├── test_assert_define.js
    ├── test_base_module1.mjs
    ├── test_base_module2.mjs
    ├── test_module_import_dynamic.js
    └── test_polyfill_date.js
```
