.class public final Lcom/github/catvod/spider/merge/h/b;
.super Lcom/github/catvod/spider/merge/e/z;
.source "SourceFile"


# static fields
.field public static final b:Lcom/github/catvod/spider/merge/h/a;


# instance fields
.field public final a:Lcom/github/catvod/spider/merge/h/w;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lcom/github/catvod/spider/merge/h/a;

    .line 2
    .line 3
    invoke-direct {v0}, Lcom/github/catvod/spider/merge/h/a;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, Lcom/github/catvod/spider/merge/h/b;->b:Lcom/github/catvod/spider/merge/h/a;

    .line 7
    .line 8
    return-void
.end method

.method public constructor <init>(Lcom/github/catvod/spider/merge/e/n;Lcom/github/catvod/spider/merge/e/z;Ljava/lang/Class;)V
    .locals 1

    .line 1
    invoke-direct {p0}, <PERSON>ja<PERSON>/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    new-instance v0, Lcom/github/catvod/spider/merge/h/w;

    .line 5
    .line 6
    invoke-direct {v0, p1, p2, p3}, Lcom/github/catvod/spider/merge/h/w;-><init>(Lcom/github/catvod/spider/merge/e/n;Lcom/github/catvod/spider/merge/e/z;Ljava/lang/reflect/Type;)V

    .line 7
    .line 8
    .line 9
    iput-object v0, p0, Lcom/github/catvod/spider/merge/h/b;->a:Lcom/github/catvod/spider/merge/h/w;

    .line 10
    .line 11
    return-void
.end method


# virtual methods
.method public final a(Lcom/github/catvod/spider/merge/l/a;Ljava/lang/Object;)V
    .locals 4

    .line 1
    if-nez p2, :cond_0

    .line 2
    .line 3
    invoke-virtual {p1}, Lcom/github/catvod/spider/merge/l/a;->i()Lcom/github/catvod/spider/merge/l/a;

    .line 4
    .line 5
    .line 6
    return-void

    .line 7
    :cond_0
    invoke-virtual {p1}, Lcom/github/catvod/spider/merge/l/a;->b()V

    .line 8
    .line 9
    .line 10
    invoke-static {p2}, Ljava/lang/reflect/Array;->getLength(Ljava/lang/Object;)I

    .line 11
    .line 12
    .line 13
    move-result v0

    .line 14
    const/4 v1, 0x0

    .line 15
    :goto_0
    if-ge v1, v0, :cond_1

    .line 16
    .line 17
    invoke-static {p2, v1}, Ljava/lang/reflect/Array;->get(Ljava/lang/Object;I)Ljava/lang/Object;

    .line 18
    .line 19
    .line 20
    move-result-object v2

    .line 21
    iget-object v3, p0, Lcom/github/catvod/spider/merge/h/b;->a:Lcom/github/catvod/spider/merge/h/w;

    .line 22
    .line 23
    invoke-virtual {v3, p1, v2}, Lcom/github/catvod/spider/merge/h/w;->a(Lcom/github/catvod/spider/merge/l/a;Ljava/lang/Object;)V

    .line 24
    .line 25
    .line 26
    add-int/lit8 v1, v1, 0x1

    .line 27
    .line 28
    goto :goto_0

    .line 29
    :cond_1
    invoke-virtual {p1}, Lcom/github/catvod/spider/merge/l/a;->e()V

    .line 30
    .line 31
    .line 32
    return-void
.end method
