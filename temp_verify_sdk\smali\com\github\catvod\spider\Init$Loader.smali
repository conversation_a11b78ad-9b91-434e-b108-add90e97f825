.class Lcom/github/catvod/spider/Init$Loader;
.super Ljava/lang/Object;
.source "SourceFile"


# static fields
.field public static volatile a:Lcom/github/catvod/spider/Init;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lcom/github/catvod/spider/Init;

    .line 2
    .line 3
    invoke-direct {v0}, Lcom/github/catvod/spider/Init;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, Lcom/github/catvod/spider/Init$Loader;->a:Lcom/github/catvod/spider/Init;

    .line 7
    .line 8
    return-void
.end method
